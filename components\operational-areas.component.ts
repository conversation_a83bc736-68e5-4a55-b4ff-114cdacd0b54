/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable sort-imports */
import * as LoadedModulesActions from '../../../shared/store/actions/loaded-modules.action';
import { AppState } from '../../../shared/store/states/app.state';
import { C2oa } from 'src/app/types/operational-areas/operational-areas.type';
import { C2oaService } from '../services/operational-areas.service';
import { Component, OnDestroy, ViewChild } from '@angular/core';
import { ContextMenuComponent, ContextMenuSelectEvent } from '@progress/kendo-angular-menu';
import { HistoryComponent } from 'src/app/shared/components/history-c2oa-and-beddown-plan/history/history.component';
import { ConfidentialityLabelHelper, DialogLevelEnum, IOViewerEnum, ObjectHelper } from '@airc2is/aus';
import { MessageUtility } from '@airc2is/vscontrols';
import { ModuleNameEnum } from 'src/app/enums/module-name.enum';
import { ReportItem } from 'src/app/types/shared/report-item.type';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { CellClickEvent } from '@progress/kendo-angular-grid';
import { C2oaMapHelper } from '../services/operational-areas-map.helper';
import { skip, Subscription } from 'rxjs';
import { Visibility } from '@airc2is/ags';
import { ObjectToXml } from 'src/app/shared/helpers/object-to-xml.helper';
import { C2oaActionStatusEnum } from '../services/enum/c2oa-action.enum';
import * as LoadedModulesSelector from '../../../shared/store/selectors/loaded-modules.selector';

const expandedSize = '700px';
const collapsedSize = '20px';
/**
 * C2oaComponent.
 */
@Component({
	selector: 'app-c2oa',
	templateUrl: './operational-areas.component.html',
	styles: [
		`.shapeImage { width: 100%; height: 20px; background-repeat: no-repeat; background-size: contain; }
         .c2oa-table { height: calc(100%) }
		 .headerBar {justify-content: space-between; }
         :host ::ng-deep th .k-link span { width:100% }`
	]
})
export class C2oaComponent implements OnDestroy {
	@ViewChild('contextMenu') public contextMenu?: ContextMenuComponent;
	public data: C2oa[] = [];

	public detailsPanel = {
		expanded: false,
		maxSplitterSize: expandedSize,
		splitterSize: collapsedSize,
		subtitle: 'Show C2OA Details',
		c2oaName: '',
		item: <C2oa>{}
	};

	public type!: string;

	public selectedKeys: number[] = [];
	public selectedKeyName = 'id';

	public isVisibleOnMapAll = false;
	public firstOnType = true;

	// needed for generating the report
	entity: ReportItem[] = [];
	show = false;
	contextMenuItems: { text: string, iconUrl: string, value: number }[];
	selectedEntityReport: C2oa = {} as C2oa;
	public isCollapsed = false;
	activeModuleName = '';
	private _subscriptions: Subscription[] = [];
	constructor(
		private _c2oaMapUtility: C2oaMapHelper,
		private _c2oaService: C2oaService, private _translate: TranslateService, private _store: Store<AppState>) {
		this._c2oaMapUtility.constructLayers(_store);
		this._store.dispatch(LoadedModulesActions.setModuleLoaded());
		this._subscriptions.push(
			this._c2oaService.listen.refresh
				.subscribe((type: string) => {
					this._c2oaService.canChangeSelection().subscribe((yes) => {
						if (yes) {
							this._getC2oaList(type);
						}
					});
				})
		);

		this._subscriptions.push(
			this._c2oaService.listen.history
				.subscribe(() => this._history())
		);

		this._subscriptions.push(
			this._c2oaService.listen.delete
				.subscribe((id) => this.deleteItem(id))
		);

		this._subscriptions.push(
			this._c2oaMapUtility.contextMenuItemClicked.subscribe((response) => {
				const entity = this.data.find((i) => i.id === response.baseIoId);
				if (ObjectHelper.isNullOrEmpty(entity)) {
					return;
				}
				this.openEntityReport(entity);
			})
		);

		this._subscriptions.push(
			this._c2oaService.listen.create.subscribe(() => {
				this.toggleDetails(true);
			})
		);

		this.contextMenuItems = [
			{
				text: 'Show Entity Report',
				iconUrl: '../../assets/visuals/queryManager/iOViewer.png',
				value: 1
			}
		];

		this._subscriptions.push(
			this._c2oaMapUtility.entityLayerSelectionChangedOnMapC20A.subscribe((response) => {
				const baseIoId = response.e.newSelectedObjects[0].baseIoId;
				if (response && baseIoId) {
					const item = this.data.find((i) => i.id === baseIoId);
					if (item) {
						this._setTableSelection(item);
					}
				}
			})
		);

		this._subscriptions.push(this._store.select(LoadedModulesSelector.getActiveModuleName).subscribe(module => {
			this.activeModuleName = module;
		}));
	}

	/**
	 * Get key as keyof C2oa, so we can use it as index signature.
	 *
	 * @returns Keyof C2oa.
	 * @private
	 */
	// eslint-disable-next-line no-restricted-syntax
	private get _keyName(): keyof C2oa {
		return this.selectedKeyName as keyof C2oa;
	}

	/**
	 * ChangeCollapsed.
	 */
	public changeCollapsed(): void {
		this.isCollapsed = !this.isCollapsed;
	}

	/**
	 * Context menu item selection.
	 *
	 * @param e .
	 */
	public onContextMenuSelect(e: ContextMenuSelectEvent): void {
		if (ObjectHelper.isNullOrEmpty(this.selectedEntityReport)) {
			return;
		}
		this.openEntityReport(this.selectedEntityReport);
	}


	/**
	 * Open entity report.
	 *
	 * @param entity C2oa Object.
	 * @returns Void.
	 */
	public openEntityReport(entity: C2oa): void {
		this.show = false;
		this.entity = [];
		this.entity = [{
			text: entity.id,
			selected: false,
			ioType: IOViewerEnum.C2OAReport,
			reportTemplate: `${IOViewerEnum[IOViewerEnum.C2OAReport]}.trdp`,
			serviceName: 'getC2oaReportData',
			sendAsLinkDescription: 'C2OA Report',
			subject: entity.c2oaName,
			applicationName: 'C2OA',
			elementId: entity.id,
			body: {
				id: entity.id,
				classification: ConfidentialityLabelHelper.getConfidentialityLabelFreeText(entity.confidentialityLabelObject || {}),
				unitOfMeasure: ObjectToXml.unitsOfMeasureToXml(),
				zuluOffset: 0,
				zuluFormat: "ddHHmm'Z' MMM yy",
				isDeleted: false
			}
		}];
		this.show = true;
	}

	/**
	 * Fires on right click.
	 *
	 * @param event Click event.
	 */
	public onRightClick(event: CellClickEvent): void {
		if (event.type === 'contextmenu') {
			this.selectedEntityReport = event.dataItem;
			this.contextMenu?.show({
				left: event.originalEvent.pageX,
				top: event.originalEvent.pageY
			});
		}
	}


	/**
	 * Check if we can change the selected row and select it accordingly.
	 *
	 * @param event C2oa.
	 */
	public onTableRowSelect(event?: C2oa[]): void {
		if (event && event[0]) {
			const isDiffSelection = this.detailsPanel.item.id !== event[0].id;
			if (isDiffSelection) {
				if (this.firstOnType) {
					this._setTableSelection(event[0]);
					this.firstOnType = false;
				} else {
					this._c2oaService
						.canChangeSelection()
						.subscribe((canChange: boolean) => {
							if (canChange) {
								this._setTableSelection(event[0]);
							} else {
								this.selectedKeys = [...this.selectedKeys];
							}
						});
				}
			}
		}
	}

	/**
	 * Toggle the details panel.
	 *
	 * @param isExpanded Boolean.
	 */
	public toggleDetails(isExpanded: boolean): void {
		this.detailsPanel.expanded = isExpanded;
		this.detailsPanel.splitterSize = isExpanded ? expandedSize : collapsedSize;

		this.detailsPanel.subtitle = isExpanded
			? this._translate.instant('translations.operational.areas.Details.Title.Hide')
			: this._translate.instant('translations.operational.areas.Details.Title.Show');
	}

	/**
	 * Set details c2oa name.
	 *
	 * @param text String.
	 */
	public setDetailsC2oaName(text: string): void {
		this.detailsPanel.c2oaName = text ? ` - ${text}` : ``;
	}

	/**
	 * Toggle item's visibility on the ags map.
	 *
	 * @param event Event.
	 * @param item C2oa item.
	 */
	public toggleMapVisibility(event: Event, item: C2oa): void {
		event.stopImmediatePropagation();
		event.preventDefault();
		if (!item.location || !item.location?.areaAgsLocation) {
			MessageUtility.showMessage({
				title: 'Information',
				content: 'This entity is not be shown on the map, because there is no defined location.',
				level: DialogLevelEnum.Warning,
				buttonText: 'Close',
				helpParameter: '#help_param'// TODO: replace help param
			});
			return;
		}

		const model = this.data.find(i => i.id === item.id);
		console.log('toggleMapVisibility model', model);
		if (model) {
			model.isVisibleOnMap = !model.isVisibleOnMap;
			this._c2oaMapUtility.zoomToEntity(item, model.isVisibleOnMap);
		}
	}

	/**
	 * Toggle all items visibility on the ags map.
	 *
	 * @param event Event.
	 */
	public toggleMapVisibilityAll(event: Event): void {
		event.stopImmediatePropagation();
		event.preventDefault();

		this.isVisibleOnMapAll = !this.isVisibleOnMapAll;
		const visibilityStatus = this.isVisibleOnMapAll ? Visibility.Visible : Visibility.Collapsed;

		this.data.forEach((item) => {
			item.isVisibleOnMap = this.isVisibleOnMapAll;
			item.location.visibility = visibilityStatus;
		});

		this._c2oaMapUtility.zoomToEntityLayer();
	}

	/**
	 * On change selection event for the c2oa model.
	 *
	 * @param event.item C20A.
	 * @param event.action Action.
	 * @param event
	 * @returns Void.
	 */
	public onLastSelectedValue(event: {item: C2oa, action: C2oaActionStatusEnum }): void {
		switch (event.action) {
			case C2oaActionStatusEnum.Add:
				this.addToList(event.item);
				break;
			case C2oaActionStatusEnum.Update:
				this.updateItemInList(event.item);
				break;
			default:
				console.error('Action is missing');
				break;
		}
	}

	/**
	 * Add C2oa to list.
	 *
	 * @param item C20A.
	 * @returns Void.
	 */
	addToList(item: C2oa): void {
		if (!item) {
			return;
		}
		this.data.push(item);
		this.data = [...this.data];
		this._c2oaService.send.c20aData.next(this.data);

		item.isVisibleOnMap = true;
		this._c2oaMapUtility.addEntityItem(item, true);

		this._setTableSelection(item);
	}

	/**
	 * Update item to list.
	 *
	 * @param item C20A.
	 * @returns Void.
	 */
	updateItemInList(item: C2oa): void {
		const index  = this.data.findIndex((i) => i.id === item.id);
		if (index === -1) {
			return;
		}
		const c20aItem = this.data[index];

		if (c20aItem) {
			c20aItem.isVisibleOnMap = true;
			this._c2oaMapUtility.zoomToEntity(c20aItem, c20aItem.isVisibleOnMap);
			this._setTableSelection(c20aItem);
		}
	}


	/**
	 * Delete C2oa to list.
	 *
	 * @param id C20A id.
	 * @returns Void.
	 */
	deleteItem(id: string | undefined): void {
		if (!id) {
			return;
		}

		this._c2oaService.remove(id).subscribe((responseId) => {
			const indexToRemove = this.data.findIndex((i) => i.id === responseId);
			const item = this.data[indexToRemove];
			this._c2oaMapUtility.removeEntityItem(item);
			if (indexToRemove !== -1) {
				this.data.splice(indexToRemove, 1);
				this.data = [...this.data];
				this._c2oaService.send.c20aData.next(this.data);

				if (indexToRemove < this.data.length) {
					this.selectedKeys = [this.data[indexToRemove].id as unknown as number];
				} else {
					this.selectedKeys = [this.data[this.data.length - 1].id as unknown as number];
				}
			}
		});
	}

	/**
	 * When selected key changed.
	 *
	 * @param event
	 */
	onSelectedKey(event: number | null): void {
		if (!ObjectHelper.isNullOrEmpty(event)) {
			this.selectedKeys = [event];
		}
	}


	/**
	 * Ng on destroy.
	 *
	 */
	public ngOnDestroy(): void {
		this._subscriptions.forEach((i) => i.unsubscribe());
		this._c2oaMapUtility.removeEntityItems(this.data);
		this._c2oaMapUtility.removeLayers();
	}

	/**
	 * Refresh the c2oa list.
	 *
	 * @param type String.
	 */
	private _getC2oaList(type: string): void {
		if (!type) {
			return;
		}
		this.data = [];
		this.selectedKeys = [];
		if (type !== this.type) {
			this.selectedKeys = [];
		}

		this.type = type;
		this._c2oaService.send.type.next(type);
		
		this._c2oaService.getC2oaList(type)
			.subscribe({
				next: (response: C2oa[]) => {
					if (ObjectHelper.isNullOrEmpty(response)) {
						return;
					}
					this.data = response;
					this._c2oaService.send.c20aData.next(response);

					this._setTableSelection(response[0]);
					this._c2oaMapUtility.addEntityItems(this.data);
				}
			});
	}

	/**
	 * Toggle item's visibility on the ags map.
	 *
	 * @param item C2oa item.
	 * @private
	 */
	private _setTableSelection(item: C2oa): void {
		this.detailsPanel.item = item;
		this.selectedKeys = [item[this._keyName] as number];
	}


	/**
	 * Show history dialog.
	 */
	private _history(): void {
		const dialogRef = MessageUtility.showCustomDialog({
			content: HistoryComponent,
			title: this._translate.instant('translations.shared.History_Of') + this.detailsPanel.item.c2oaName,
			width: 1000,
			height: 650,
			actions: [{ text: this._translate.instant('translations.shared.Close') }]
		});

		const itemHistoryComponent = dialogRef.content.instance as HistoryComponent;
		itemHistoryComponent.moduleName = ModuleNameEnum.C2oa;
		itemHistoryComponent.itemId = this.detailsPanel.item.id;
		itemHistoryComponent.itemName = this.detailsPanel.item.c2oaName;
		itemHistoryComponent.itemCreatedBy = this.detailsPanel.item.createdBy;
		itemHistoryComponent.itemDateCreated = this.detailsPanel.item.dateCreated;
		itemHistoryComponent.itemModifiedBy = this.detailsPanel.item.lastModifiedBy;
		itemHistoryComponent.itemLastModificationDate = this.detailsPanel.item.lastModification;
		itemHistoryComponent.itemApprovedBy = this.detailsPanel.item.approvedBy || 'N/A';
		itemHistoryComponent.itemDateApproved = this.detailsPanel.item.approvalDate || 'N/A';
	}
}
