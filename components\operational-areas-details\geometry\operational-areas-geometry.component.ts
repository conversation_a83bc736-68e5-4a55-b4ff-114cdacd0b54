/* eslint-disable sort-imports */
import { A<PERSON><PERSON><PERSON><PERSON>, AirC2IoLayer, GeometryConverter, ShapeControlComponent, ShapeTypes, ShapeTypesType, Visibility } from '@airc2is/ags';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, Output } from '@angular/core';
import { C2oa } from 'src/app/types/operational-areas';
import { C2oaMapHelper } from '../../../services/operational-areas-map.helper';
import { C2oaHelper } from '../../../services/operarationa-areas.helper';
import { ApplyRequestedEventArgs } from '@airc2is/ags/lib/Presentation/Controls/LocationControl/Events/shape-control-event-args';
import { WktConverter } from 'src/app/shared/converters/wkt-converter/wkt-converter';
import { ObjectHelper, PreferenceManager, PreferenceTypeEnum } from '@airc2is/aus';
import { C2OTypesType } from '../../../services/enum/c20a-types.enum';
import Polyline from '@arcgis/core/geometry/Polyline';
import SpatialReference from '@arcgis/core/geometry/SpatialReference';
import { Subscription } from 'rxjs';
/**
 *
 */
@Component({
	selector: 'app-c2oa-geometry',
	templateUrl: './operational-areas-geometry.component.html',
	styles: []
})
export class C2oaGeometryComponent implements OnChanges, OnDestroy {
	@Input() model: C2oa | undefined;
	@Output() modelChange = new EventEmitter();
	@Input() isReadonly = true;
	ioGeometry!: AgsShape | undefined;
	shapeTypes = ShapeTypes;
	unitOfMeasure: any;
	editableStateValue = true;
	visible: Visibility = Visibility.Visible;
	collapsed: Visibility = Visibility.Collapsed;
	shapeSources: ShapeTypesType[] = [];
	private _subscriptions: Subscription[] = [];
	constructor(private _c2oaMapUtility: C2oaMapHelper) {
		this.unitOfMeasure = PreferenceManager.getInstance().getPreferenceValue(PreferenceTypeEnum.UnitOfMeasures);

		this._subscriptions.push(
			this._c2oaMapUtility.entityChanged.subscribe((response) => {
				this.trackLocationChanged(response);
			})
		);
	}

	/**
	 * Ng on changes.
	 *
	 */
	ngOnChanges(): void {
		this.shapeSources = [];
		this.ioGeometry = {} as AgsShape;
		this.displayGeometry();
	}

	/**
	 * Shape control property changed.
	 *
	 * @param e Any.
	 * @param e.sender ShapeControlComponent.
	 * @param e.args DependencyPropertyChangedEventArgs.
	 */
	shapeControlPropertyChanged(e: { sender: ShapeControlComponent; args: ApplyRequestedEventArgs; }): void {
		if (ObjectHelper.isNullOrEmpty(this.model)) {
			return;
		}

		const areaLocation = WktConverter.convertToWkt(e.sender.selectedShape);
		const areaAgsLocation = e.sender.agsShape?.toString();

		if (this.model.c2oaType === C2OTypesType.FLOT) {
			const line = GeometryConverter.convertToEsriGeometry(e.sender.agsShape);
			if (line && line.spatialReference) {
				line.spatialReference = new SpatialReference();
			}
			const shape = AirC2IoLayer.drawCircleLine(line as Polyline);
			if (shape) {
				this.model.location.areaAgsLocation = shape?.toString();
				this.model.location.areaLocation = WktConverter.convertToWkt(shape);
			}
		} else {
			this.model.location.areaAgsLocation = areaAgsLocation;
			this.model.location.areaLocation = areaLocation;
		}

		if (this.model.isNew) {
			this._c2oaMapUtility.addEntityItem(this.model, true);
		}

		this.modelChange.emit(this.model);
	}

	/**
	 * Display geometry.
	 *
	 */
	displayGeometry(): void {
		if (!this.model) {
			return;
		}
		this.ioGeometry = this._c2oaMapUtility.getIoGeometry(this.model);
		this.shapeSources = [...C2oaHelper.getShapeSources(this.model)];
	}

	/**
	 * Tracks the location changed.
	 *
	 * @param obj IBaseIo.
	 * @returns Void.
	 */
	trackLocationChanged(obj: AirC2IoLayer): void {
		if (ObjectHelper.isNullOrEmpty(obj) || ObjectHelper.isNullOrEmpty(this.model)) {
			return;
		}

		if (obj && obj.selectedItems && obj.selectedItems.length > 0 && obj.selectedItems[0].ioGeometry) {
			const areaAgsLocation = obj.selectedItems[0].ioGeometry.toString();
			const areaLocation = WktConverter.convertToWkt(obj.selectedItems[0].ioGeometry);

			this.model.location.areaAgsLocation = areaAgsLocation;
			this.model.location.areaLocation = areaLocation;
			this.modelChange.emit(this.model);

			this._c2oaMapUtility.updateEntityLocation(this.model);

			this.ioGeometry = this._c2oaMapUtility.getIoGeometry(this.model);
		}
	}

	/**
	 * Ng on destroy.
	 */
	ngOnDestroy(): void {
		this._subscriptions.forEach((i) => i.unsubscribe());
	}
}
