import { AgsModule } from '@airc2is/ags';
import { C2oaActivityStatusComponent } from './shared/components/operational-areas-activity-status.component';
import { C2oaAlertComponent } from './components/operational-areas-details/alert/operational-areas-alert.component';
import { C2oaComponent } from './components/operational-areas.component';
import { C2oaDetailsComponent } from './components/operational-areas-details/operational-areas-details.component';
import { C2oaGeometryComponent } from './components/operational-areas-details/geometry/operational-areas-geometry.component';
import { C2oaRoutingModule } from './operational-areas-routing.module';
import { C2oaStatusComponent } from './components/operational-areas-details/status/operational-areas-status.component';
import { C2oaTimeComponent } from './components/operational-areas-details/time/operational-areas-time.component';
import { CommentComponent } from './components/operational-areas-details/status/comment/comment.component';
import { CommonModule } from '@angular/common';
import { CoreModule } from 'src/app/core/core.module';
import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { SidepanelModule } from '../side-panel/sidepanel.module';
import { TranslateModule } from '@ngx-translate/core';

/**
 *
 */
@NgModule({
	declarations: [
		C2oaComponent,
		C2oaDetailsComponent,
		C2oaAlertComponent,
		C2oaGeometryComponent,
		C2oaTimeComponent,
		C2oaStatusComponent,
		C2oaActivityStatusComponent,
		CommentComponent
	],
	imports: [
		CoreModule,
		CommonModule,
		FormsModule,
		C2oaRoutingModule,
		SidepanelModule,
		SharedModule,
		TranslateModule,
		AgsModule
	],
	exports: [
		C2oaComponent,
		C2oaDetailsComponent
	],
	providers: []
})
export class C2oaModule { }
