
<div id="c2oaPanel" [class]="showRibbonTabContent ? 'sidePanelContent wRibbon c2oa' : 'sidePanelContent woRibbon c2oa'" (keydown)="keydown($event)" tabindex="0">
	<div class="sidePanelItem" (click)="select('All')" [ngClass]="{'selected': isAllSelected}">
		<div class="flex m-1 items-center">
			<div class="sidePanelIconContainer">
				<svg-icon src="assets/visuals/c2oa_all.svg" [svgStyle]="svgStyle">
				</svg-icon>
				<span class="name ml-2" kendoTooltip class="name ml-2" position="bottom" title="{{'translations.operational.areas.Shared.All'|translate}}">
					{{'translations.operational.areas.Shared.All'|translate}}
				</span>
			</div>
		</div>
	</div>
	<div *ngFor="let item of types" class="sidePanelItem" (click)="select(item.shortName)"
		[ngClass]="{'selected': selectedType === item.shortName}">
		<div class="flex m-1 items-center" title='C2OA Type {{item.name}}' kendoTooltip	class="bottom" position="bottom">
			<div class="sidePanelIconContainer">
				<img [src]="item.icon">
				<span class="name ml-2">{{ item.shortName }}</span>
			</div>
		</div>
	</div>
</div>