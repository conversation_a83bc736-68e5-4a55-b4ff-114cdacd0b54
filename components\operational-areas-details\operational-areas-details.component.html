
<div (click)="layoutClick($event)" *ngIf="model">

	<kendo-gridlayout [gap]="15" [rows]="[60]" [cols]="['46%', '25%', '25%']" class="mt-2">

		<kendo-gridlayout-item [row]="1">
			<vs-textbox name="Name" [label]="'translations.operational.areas.Details.Name'|translate"
				[title]="'translations.operational.areas.Details.Tooltips.Name'|translate" [required]="true"
				[readonly]="isEdit || isReadonly || model.isDisabled" [(ngModel)]="model.c2oaName" [maxlength]="25">
			</vs-textbox>
		</kendo-gridlayout-item>

		<kendo-gridlayout-item [row]="1">
			<vs-dropdownlist [data]="types" [required]="true" textField="key" valueField="key" [readonly]="isEdit || isReadonly ||  model.isDisabled"
				[title]="'translations.operational.areas.Details.Tooltips.Type'|translate"
				[label]="'translations.operational.areas.Details.Type'|translate" [(ngModel)]="model.c2oaType"
				(valueChange)="typeValueChange()">
			</vs-dropdownlist>
		</kendo-gridlayout-item>

		<kendo-gridlayout-item [row]="1" class="flex gap-x-2.5 items-center h-full pt-3">
			<app-c2oa-activity-status *ngIf="model && model.activityStatus && !model.isDisabled" [activityStatus]="model.activityStatus"
				[timePeriods]="model.timePeriods"></app-c2oa-activity-status>
		</kendo-gridlayout-item>

	</kendo-gridlayout>


	<kendo-gridlayout [gap]="15" [rows]="[60]" [cols]="['46%', '25%', '25%']" class="mt-2">

		<kendo-gridlayout-item [row]="1" class="flex gap-x-3.5">
			<vs-textbox [attr.name]="fields.altitudeMin.name" [required]="fields.altitudeMin.required" style="text-transform: uppercase;" 
				[title]="'translations.operational.areas.Details.Tooltips.AltitudeMin'|translate"
				[label]="'translations.operational.areas.Details.AltitudeMin'|translate" [readonly]="isAltitudeDisabled || isReadonly || model.isDisabled"
				[value]="model.altitudeMin" [ngClass]="{'ng-invalid':fields.altitudeMin.error }"
				(valueChange)="onInputChange($event, 'altitudeMin')"></vs-textbox>

			<ng-container [ngTemplateOutlet]="altitudeError" *ngIf="fields.altitudeMin.showError"></ng-container>

			<vs-textbox [attr.name]="fields.altitudeMax.name" [required]="fields.altitudeMax.required" style="text-transform: uppercase;" 
				[title]="'translations.operational.areas.Details.Tooltips.AltitudeMax'|translate"
				[label]="'translations.operational.areas.Details.AltitudeMax'|translate" [readonly]="isAltitudeDisabled || isReadonly || model.isDisabled"
				[value]="model.altitudeMax" [ngClass]="{'ng-invalid':fields.altitudeMax.error }"
				(valueChange)="onInputChange($event, 'altitudeMax')"></vs-textbox>

			<ng-container [ngTemplateOutlet]="altitudeError" *ngIf="fields.altitudeMax.showError"></ng-container>

		</kendo-gridlayout-item>

		<kendo-gridlayout-item [row]="1" class="flex items-center h-full pt-3">
			<vs-checkbox [label]="'translations.operational.areas.Details.Simulated'|translate" class="flex gap-x-2.5"
				[title]="'translations.operational.areas.Details.Tooltips.Simulated'|translate"
				[(ngModel)]="model.isSimulated" [disabled]="isReadonly || model.isDisabled">
			</vs-checkbox>
		</kendo-gridlayout-item>

		<kendo-gridlayout-item [row]="1" class="flex gap-x-2.5 items-center h-full pt-3"
			[title]="'translations.operational.areas.Details.Tooltips.Status'|translate">
			<ng-container *ngIf="Status[model.approvalStatus] && !model.isDisabled">
				<img [src]="Status[model.approvalStatus].iconUrl" style="height: 18px; width: 18px;">
				<strong [innerText]="Status[model.approvalStatus].label || '-'"></strong>
			</ng-container>
		</kendo-gridlayout-item>

	</kendo-gridlayout>

	<kendo-gridlayout [gap]="15" [rows]="[60]" [cols]="['100%']" class="mt-2">
		<vs-textbox name="Description" [label]="'translations.operational.areas.Details.Description'|translate"
		[title]="'translations.operational.areas.Details.Tooltips.Details'|translate"
			[(ngModel)]="model.description" [readonly]="isReadonly || model.isDisabled" [maxlength]="249">
		</vs-textbox>
	</kendo-gridlayout>

 	<kendo-tabstrip>
        <kendo-tabstrip-tab [title]="'translations.operational.areas.Details.Geometry.Title'|translate" [selected]="true">
          <ng-template kendoTabContent>
				<app-c2oa-geometry [(model)]="model" [isReadonly]="isReadonly"></app-c2oa-geometry>
          </ng-template>
        </kendo-tabstrip-tab>
        <kendo-tabstrip-tab [title]="'translations.operational.areas.Details.Time.Title'|translate">
          <ng-template kendoTabContent>
				<app-c2oa-time [(model)]="model" [isReadonly]="isReadonly"></app-c2oa-time>
          </ng-template>
        </kendo-tabstrip-tab>
        <kendo-tabstrip-tab [title]="'translations.operational.areas.Details.Alert.Title'|translate">
          <ng-template kendoTabContent>
				<app-c2oa-alert [(model)]="model" [isReadonly]="isReadonly"></app-c2oa-alert>
          </ng-template>
        </kendo-tabstrip-tab>
    </kendo-tabstrip>
</div>

<app-c2oa-status [model]="model" (statusChanged)="onStatusChange($event)" *ngIf="model"></app-c2oa-status>

<ng-template #altitudeError>
	<div class="altitudeInvalid">
		<div [innerHtml]="'translations.operational.areas.Details.Warnings.Altitude'|translate">
		</div>
	</div>
</ng-template>
