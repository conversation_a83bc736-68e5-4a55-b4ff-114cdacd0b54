/* eslint-disable sort-imports */
import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { DialogAction, DialogResult } from '@progress/kendo-angular-dialog';

import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { C2oa } from 'src/app/types/operational-areas';
import { C2oaService } from '../../../services/operational-areas.service';
import { MessageUtility } from '@airc2is/vscontrols';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { CommentComponent } from './comment/comment.component';

/**
 * C2oaStatusComponent.
 */
@Component({
	selector: 'app-c2oa-status',
	template: '',
	styleUrls: []
})

export class C2oaStatusComponent implements OnDestroy {
	@Input() model!: C2oa;
	@Output() statusChanged = new EventEmitter<C2oa>();

	private _subscriptions: Subscription[] = [];

	constructor(
		private _c2oaService: C2oaService,
		private _translate: TranslateService) {
		this._subscriptions.push(
			this._c2oaService.listen.status
				.subscribe((status: ApprovalStatusEnum) => this._openJustify(status)));
	}

	/**
	 * Ng on destroy.
	 */
	ngOnDestroy(): void {
		this._subscriptions.forEach((i) => i.unsubscribe());
	}

	/**
	 * Send status event.
	 *
	 * @param status String, value of ApprovalStatusEnum.
	 */
	private _openJustify(status: ApprovalStatusEnum): void {
		const justifyText = this._translate.instant('translations.operational.areas.Ribbon.StatusChange.Justify');
		const dialog = MessageUtility.showCustomDialog({
			title: this._translate.instant(`translations.operational.areas.Ribbon.StatusChange.${status}`),
			content: CommentComponent,
			actions: [
				{ text: justifyText },
				{ text: this._translate.instant('translations.shared.Cancel') }],
			width: 400,
			height: 300
		});

		const context = dialog.content.instance as CommentComponent;

		dialog.result.subscribe((action: DialogResult) => {
			if ((action as DialogAction).text === justifyText) {
				const comment = context.latestCommentValue;
				this._status(status, comment);
			}
		});
	}

	/**
	 * Send status event.
	 *
	 * @param name String, value of ApprovalStatusEnum.
	 * @param comment String.
	 */
	private _status(name: ApprovalStatusEnum, comment: string | null): void {
		if (!this.model.id) {
			return;
		}
		const model = {
			...this.model,
			approvalComment: comment
		};

		this._subscriptions.push(
			this._c2oaService.changeStatus(model, name)
				.subscribe(() => this.statusChanged.emit(model))
		);
	}
}
