import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oaActivityStatusComponent } from './operational-areas-activity-status.component';
import { CoreModule } from 'src/app/core/core.module';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideMockStore } from '@ngrx/store/testing';


describe('C2oaActivityStatusComponent', () => {
	let component: C2oaActivityStatusComponent;
	let fixture: ComponentFixture<C2oaActivityStatusComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, FormsModule, CoreModule, BrowserAnimationsModule],
					declarations: [C2oaActivityStatusComponent],
					providers: [provideMockStore({})]
				});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaActivityStatusComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());
});
