import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { DistanceUnitsEnum, KeyValueType, UnitConversionHelper, UnitOfMeasure, UnitTypeEnum } from '@airc2is/aus';

import { AffiliationEnum } from 'src/app/enums/affiliation.enum';
import { C2oa } from 'src/app/types/operational-areas';
import { ObjectTypeEnum } from 'src/app/enums/object-type.enum';

/**
 *
 */
@Component({
	selector: 'app-c2oa-alert',
	templateUrl: './operational-areas-alert.component.html',
	styles: []
})
export class C2oaAlertComponent implements OnChanges {
	@Input() model: C2oa | undefined;
	@Output() modelChange = new EventEmitter<C2oa>();

	@Input() isReadonly = true;

	isDisabled = true;

	affiliation?: string;
	affiliations = Object.values(AffiliationEnum)
		.filter(value => ![AffiliationEnum.UND, AffiliationEnum.OTR].includes(value))
		.map<KeyValueType>((key, index) => ({
			value: index,
			key
		}));

	objectType?: string;
	unitTypes = Object.values(ObjectTypeEnum)
		.map<KeyValueType>((key, index) => ({
			value: index,
			key
		}));

	distanceType = UnitTypeEnum.Distance;

	coverage: { value: number | null, type?: string } = { value: 0 };

	constructor() {
		//
	}

	/**
	 * Ng on changes.
	 */
	ngOnChanges(): void {
		this.affiliation = AffiliationEnum[this.model?.affiliation as keyof typeof AffiliationEnum];
		this.objectType = ObjectTypeEnum[this.model?.objectType as keyof typeof ObjectTypeEnum];
		this.coverage.value = this.model?.coverage ?? 0;
		this._setDisabled();
	}

	/**
	 * On model change, emit.
	 */
	onChange(): void {
		if (this.model) {
			this._setDisabled();

			if (!this.isDisabled) {
				this.model.affiliation = this._keyEnum(AffiliationEnum, this.affiliation);
				this.model.objectType = this._keyEnum(ObjectTypeEnum, this.objectType);
			}

			this.modelChange.emit(this.model);
		}
	}

	/**
	 * On coverage change.
	 *
	 * @param value Number.
	 * @returns VOID.
	 */
	coverageChange(value: number | null): void {
		this.coverage.value = value;

		const unitOfMeasure = new UnitOfMeasure();
		unitOfMeasure.distance = DistanceUnitsEnum.Meter;

		const roundedValue = this._round(UnitConversionHelper.factoryUnitTypeMapperFromBase(this.distanceType, unitOfMeasure, this.coverage.value as number), 2);
		if (this.model) {
			this.model.coverage = roundedValue;
			this.onChange();
		}
	}

	// onTypeComboboxValueChange(value: UnitEntityType): void {
	// 	if (!value) {
	// 		return;
	// 	}
	// 	const unitOfMeasure = new UnitOfMeasure();
	// 	unitOfMeasure.distance = DistanceUnitsEnum.Meter;

	// 	const roundedValue = this._round(UnitConversionHelper
	// 		.factoryUnitTypeMapperFromBase(this.distanceType, unitOfMeasure, this.coverage.value as number), 2);
	// 	console.log('roundedValue - onTypeComboboxValueChange', roundedValue);
	// 	if (this.model) {
	// 		this.model.coverage = roundedValue;
	// 		this.onChange();
	// 	}
	// }


	/**
	 * Set the disabled variable based on enableAlert.
	 */
	private _setDisabled(): void {
		this.isDisabled = !this.model?.alertEnabled;

		if (this.isDisabled && this.model) {
			this.model.coverage = null;
			this.model.objectType = null;
			this.model.affiliation = null;
			this.model.isPlanned = false;

			this.coverage = { value: 0 };
		}
	}

	/**
	 * Round number to two digit.
	 *
	 * @param value Number to be rounded.
	 * @param precision Digit number to be rounded.
	 * @returns Rounded number.
	 * @private
	 */
	private _round(value: number, precision?: number): number {
		if (!precision) {
			precision = 2;
		}
		const multiplier = Math.pow(10, precision || 0);
		return Math.round(value * multiplier) / multiplier;
	}

	/**
	 *_keyEnum.
	 *
	 * @param value .
	 * @returns String .
	 */
	private _keyEnum(objEnum: { [x: string]: string }, value?: string): string | null {
		if (!value) {
			return null;
		}

		let key = '';
		const keys = Object.keys(objEnum);
		Object.values(objEnum)
			.find((val: string, index: number) => {
				if (val === value) {
					key = keys[index];
					return true;
				}
				return false;
			});

		return key ?? null;
	}
}
