/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oaAlertComponent } from './operational-areas-alert.component';
import { C2oaService } from '../../../services/operational-areas.service';
import { CoreModule } from 'src/app/core/core.module';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { UnitTypeEnum } from '@airc2is/aus';

import { dummySingleGetResponse } from '../../../services/mocks';
import { provideMockStore } from '@ngrx/store/testing';

describe('C2oaAlertComponent', () => {
	let component: C2oaAlertComponent;
	let fixture: ComponentFixture<C2oaAlertComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, FormsModule, CoreModule, BrowserAnimationsModule],
					declarations: [C2oaAlertComponent],
					providers: [C2oaService, provideMockStore({})]
				});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaAlertComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		component.isDisabled = true;
		component.distanceType = UnitTypeEnum.Distance;
		expect(component).toBeTruthy();
	});

	it('check affiliations', () => {
		expect(component.affiliations[0].key).toEqual('Friendly');
		expect(component.affiliations[1].key).toEqual('Enemy');
		expect(component.affiliations[2].key).toEqual('Neutral');
	});

	it('ng changes', () => component.ngOnChanges());

	it('onChange, with model and readonly true', () => {
		component.isReadonly = true;
		component.model = dummySingleGetResponse;
		component.model.alertEnabled = false;

		component.modelChange.subscribe((response) => {
			expect(response.id).toEqual(dummySingleGetResponse.id);
		});

		component.onChange();

		expect(component.model.coverage).toBeNull();
		expect(component.model.objectType).toBeNull();
		expect(component.model.affiliation).toBeNull();
		expect(component.model.isPlanned).toEqual(false);

		expect(component.coverage.value).toEqual(0);
	});

	it('onChange, with model and readonly false', () => {
		component.model = dummySingleGetResponse;
		component.model.alertEnabled = true;
		component.isReadonly = false;

		component.modelChange.subscribe((response) => {
			expect(response.alertEnabled).toEqual(true);
		});

		component.onChange();

		expect(component.coverage.value).toEqual(0);
	});

	it('onChange, without model', () => {
		// eslint-disable-next-line no-undefined
		component.model = undefined;
		component.onChange();
	});

	it('coverage change, value, with type and model', () => {
		component.model = dummySingleGetResponse;
		component.model.alertEnabled = true;
		component.isReadonly = false;
		component.coverageChange(100, 'idk');
		expect(component.model.coverage).toEqual(100);
	});

	it('private _round with no precision', () => {
		expect(component['_round'](100)).toEqual(100);
	});
});
