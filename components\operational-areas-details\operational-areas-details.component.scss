.altitudeInvalid {

    position: relative;
    margin-right: -14px;

    >div {
        z-index: 9;
        position: absolute;
        width: 300px;
        background: #c31d1d;
        padding: 5px;
        border-radius: 4px;
        top: 19px;
        font-weight: 600;
        margin-left: -8px;
    }
}

.tabsContainer {
    min-height: 200px;
}

vs-textbox.ng-invalid ::ng-deep {
    kendo-textbox {
        border-color: #c31d1d;
    }
}



::ng-deep {
    .k-tabstrip-top > .k-content {
        padding: 10px 0px;
    }
    .sidePanelItemOverview .k-tabstrip-top > .k-tabstrip-items-wrapper .k-item {
        color: #fff;
    }
}

