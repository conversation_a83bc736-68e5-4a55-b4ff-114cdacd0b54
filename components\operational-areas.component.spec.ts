import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oaAlertComponent } from './operational-areas-details/alert/operational-areas-alert.component';
import { C2oaComponent } from './operational-areas.component';
import { C2oaDetailsComponent } from './operational-areas-details/operational-areas-details.component';
import { C2oaGeometryComponent } from './operational-areas-details/geometry/operational-areas-geometry.component';
import { C2oaService } from '../services/operational-areas.service';
import { C2oaTimeComponent } from './operational-areas-details/time/operational-areas-time.component';
import { CoreModule } from 'src/app/core/core.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from 'src/app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

import { dummyMultipleGetResponse } from '../services/mocks';

import { of } from 'rxjs';
import { provideMockStore } from '@ngrx/store/testing';


describe('C2oaComponent', () => {
	let component: C2oaComponent;
	let fixture: ComponentFixture<C2oaComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, SharedModule, TranslateModule.forRoot(), CoreModule, BrowserAnimationsModule],
					declarations: [C2oaComponent, C2oaDetailsComponent, C2oaAlertComponent, C2oaGeometryComponent, C2oaTimeComponent],
					providers: [C2oaService, provideMockStore({})]
				})
			.compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => (expect(component).toBeTruthy()));
	it('should have type default set to all', () => expect(component.type).toEqual('ALL'));
	it('should have selectedKeys default set to empty array', () => expect(component.selectedKeys.length).toEqual(0));
	it('should have isVisibleOnMapAll default set to false', () => expect(component.isVisibleOnMapAll).toEqual(false));
	it('should have firstOnType default set to true', () => expect(component.firstOnType).toEqual(true));
	it('should have detailsPanel default set to true', () => expect(component.detailsPanel.expanded).toEqual(false));

	it('should change c2oa details panel name', () => {
		component.setDetailsC2oaName('dummy name');
		expect(component.detailsPanel.c2oaName).toBe(' - dummy name');
	});

	it('should change c2oa details panel name to empty string', () => {
		component.setDetailsC2oaName('');
		expect(component.detailsPanel.c2oaName).toBe('');
	});

	it('should toggle details with isExpanded:true', () => {
		component.toggleDetails(true);
		expect(component.detailsPanel.expanded).toEqual(true);
		expect(component.detailsPanel.splitterSize).toEqual('850px');
		expect(component.detailsPanel.subtitle).toEqual('Hide C2OA Details');
	});

	it('should toggle details with isExpanded:false', () => {
		component.toggleDetails(false);
		expect(component.detailsPanel.expanded).toEqual(false);
		expect(component.detailsPanel.splitterSize).toEqual('40px');
		expect(component.detailsPanel.subtitle).toEqual('Show C2OA Details');
	});

	it('should toggle map visibility for item', () => {
		component.data = dummyMultipleGetResponse;

		const evt = new Event('click', {
			bubbles: true,
			cancelable: true
		});

		const isVisibleOnMapBefore = component.data[0].isVisibleOnMap;

		component.toggleMapVisibility(evt, component.data[0]);

		expect(component.data[0].isVisibleOnMap).toEqual(!isVisibleOnMapBefore);
	});

	it('should toggle map visibility for all items:isVisibleOnMapAll', () => {
		component.data = dummyMultipleGetResponse;

		const evt = new Event('click', {
			bubbles: true,
			cancelable: true
		});

		const isVisibleOnMapBefore = component.isVisibleOnMapAll;

		component.toggleMapVisibilityAll(evt);

		expect(component.isVisibleOnMapAll).toEqual(!isVisibleOnMapBefore);

		const allIsVisibleCount = component.data.filter(item => item.isVisibleOnMap === component.isVisibleOnMapAll).length;

		expect(allIsVisibleCount).toEqual(component.data.length);
	});

	it('should select: void', () => {
		component.select();
	});

	it('should select: first on type true => check first on type false', () => {
		component.data = dummyMultipleGetResponse;
		component.firstOnType = true;
		component.selectedKeyName = 'visualMatrixId';
		component.select([component.data[0]]);

		expect(component.firstOnType).toEqual(false);
		expect(component.detailsPanel.item).toEqual(component.data[0]);
		expect(component.selectedKeys).toEqual([component.data[0].visualMatrixId as number]);
	});

	it('should select: first on type false => details panel item', () => {
		component.data = dummyMultipleGetResponse;
		component.firstOnType = false;
		component.selectedKeyName = 'visualMatrixId';
		component.select([component.data[0]]);

		const service = fixture.debugElement.injector.get(C2oaService);

		spyOn(service, 'canChangeSelection').and.returnValue(of(true));

		expect(component.detailsPanel.item).toEqual(component.data[0]);
		expect(component.selectedKeys).toEqual([component.data[0].visualMatrixId as number]);
	});
});
