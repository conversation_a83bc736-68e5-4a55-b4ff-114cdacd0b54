/* eslint-disable sort-imports */
import { C2OTypesType } from './enum/c20a-types.enum';
import { Injectable } from '@angular/core';
import { ShapeTypes, ShapeTypesType } from '@airc2is/ags';
import { C2oa } from 'src/app/types/operational-areas';

/**
 *
 */
@Injectable({ providedIn: 'root' })
export class C2oaHelper {
	constructor() {
		//
	}


	/**
	 * Get shape type.
	 *
	 * @param type C2OTypesType enum value.
	 * @returns Shape type.
	 */
	public static getShapeType(type: C2OTypesType): ShapeTypes {
		switch (type) {
			case C2OTypesType.JFSCL:
			case C2OTypesType.FSCM:
			case C2OTypesType.FSSL:
			case C2OTypesType.FLOT:
				return ShapeTypes.AgsLine;
			case C2OTypesType.PIM:
			case C2OTypesType.DAofTBMD:
				return ShapeTypes.AgsPointWithArrow;
			case C2OTypesType.Generic:
				return ShapeTypes.AgsPoint;
			default:
				return ShapeTypes.AgsPolygon;
		}
	}


	/**
	 * Set shape source.
	 *
	 * @param item C2oa.
	 * @returns ShapeTypesType aray.
	 */
	public static getShapeSources(item: C2oa): ShapeTypesType[] {
		const selectedC2oaType: C2OTypesType = C2OTypesType[item.c2oaType as keyof typeof C2OTypesType];
		let shapeSources: ShapeTypesType[] = [];
		switch (selectedC2oaType) {
			case C2OTypesType.JOA:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					}
				];
				break;
			case C2OTypesType.AOR:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					}
				];
				break;
			case C2OTypesType.JFSCL:
				shapeSources = [
					{
						key: 'Polyline',
						value: ShapeTypes.AgsLine
					}, {
						key: 'Freehand Polyline',
						value: ShapeTypes.AgsFreehandPolyline
					}];
				break;

			case C2OTypesType.FSCM:
				shapeSources = [
					{
						key: 'Point',
						value: ShapeTypes.AgsPoint
					},
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Polyline',
						value: ShapeTypes.AgsLine
					},
					{
						key: 'Ellipse',
						value: ShapeTypes.AgsEllipse
					},
					{
						key: 'Corridor',
						value: ShapeTypes.AgsCorridor
					},
					{
						key: 'Orbit',
						value: ShapeTypes.AgsOrbit
					},
					{
						key: 'PolyArc',
						value: ShapeTypes.AgsPolyArc
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					},
					{
						key: 'Freehand Polyline',
						value: ShapeTypes.AgsFreehandPolyline
					}];
				break;

			case C2OTypesType.FSSL:
			case C2OTypesType.FLOT:
				shapeSources = [
					{
						key: 'Polyline',
						value: ShapeTypes.AgsLine
					}, {
						key: 'Freehand Polyline',
						value: ShapeTypes.AgsFreehandPolyline
					}];
				break;

			case C2OTypesType.PIM:
				shapeSources = [
					{
						key: 'Point with Arrow',
						value: ShapeTypes.AgsPointWithArrow
					}];
				break;

			case C2OTypesType.DAofTBMD:
				shapeSources = [
					{
						key: 'Point',
						value: ShapeTypes.AgsPoint
					},
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Polyline',
						value: ShapeTypes.AgsLine
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					},
					{
						key: 'Ellipse',
						value: ShapeTypes.AgsEllipse
					},
					{
						key: 'Track',
						value: ShapeTypes.AgsTrack
					},
					{
						key: 'Corridor',
						value: ShapeTypes.AgsCorridor
					},
					{
						key: 'Orbit',
						value: ShapeTypes.AgsOrbit
					},
					{
						key: 'PolyArc',
						value: ShapeTypes.AgsPolyArc
					},
					{
						key: 'Point with Arrow',
						value: ShapeTypes.AgsPointWithArrow
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					},
					{
						key: 'Freehand Polyline',
						value: ShapeTypes.AgsFreehandPolyline
					}];
				break;

			case C2OTypesType.TOA:
			case C2OTypesType.TTA:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					},
					{
						key: 'Ellipse',
						value: ShapeTypes.AgsEllipse
					},
					{
						key: 'Track',
						value: ShapeTypes.AgsTrack
					},
					{
						key: 'Corridor',
						value: ShapeTypes.AgsCorridor
					},
					{
						key: 'Orbit',
						value: ShapeTypes.AgsOrbit
					},
					{
						key: 'PolyArc',
						value: ShapeTypes.AgsPolyArc
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					}];
				break;

			case C2OTypesType.KB:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					},
					{
						key: 'Track',
						value: ShapeTypes.AgsTrack
					},
					{
						key: 'Corridor',
						value: ShapeTypes.AgsCorridor
					}, {
						key: 'PolyArc',
						value: ShapeTypes.AgsPolyArc
					}, {
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					}];
				break;

			case C2OTypesType.ROZ:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					},
					{
						key: 'Track',
						value: ShapeTypes.AgsTrack
					},
					{
						key: 'Corridor',
						value: ShapeTypes.AgsCorridor
					},
					{
						key: 'Orbit',
						value: ShapeTypes.AgsOrbit
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					}];
				break;

			case C2OTypesType.MEZ:
				shapeSources = [
					{
						key: 'Polygon',
						value: ShapeTypes.AgsPolygon
					},
					{
						key: 'Circle',
						value: ShapeTypes.AgsCircle
					},
					{
						key: 'PolyArc',
						value: ShapeTypes.AgsPolyArc
					},
					{
						key: 'RadArc',
						value: ShapeTypes.AgsRadArc
					},
					{
						key: 'Freehand Polygon',
						value: ShapeTypes.AgsFreehandPolygon
					}];
				break;
			default:
				shapeSources = [{
					key: 'Point',
					value: ShapeTypes.AgsPoint
				}, {
					key: 'Polygon',
					value: ShapeTypes.AgsPolygon
				}, {
					key: 'Polyline',
					value: ShapeTypes.AgsLine
				}, {
					key: 'Circle',
					value: ShapeTypes.AgsCircle
				}, {
					key: 'Ellipse',
					value: ShapeTypes.AgsEllipse
				}, {
					key: 'Track',
					value: ShapeTypes.AgsTrack
				}, {
					key: 'Corridor',
					value: ShapeTypes.AgsCorridor
				}, {
					key: 'Orbit',
					value: ShapeTypes.AgsOrbit
				}, {
					key: 'Polyarc',
					value: ShapeTypes.AgsPolyArc
				}, {
					key: 'RadArc',
					value: ShapeTypes.AgsRadArc
				}, {
					key: 'Point with Arrow',
					value: ShapeTypes.AgsPointWithArrow
				}, {
					key: 'Freehand Polygon',
					value: ShapeTypes.AgsFreehandPolygon
				}, {
					key: 'Freehand Polyline',
					value: ShapeTypes.AgsFreehandPolyline
				}];
				break;
		}
		return shapeSources;
	}
}
