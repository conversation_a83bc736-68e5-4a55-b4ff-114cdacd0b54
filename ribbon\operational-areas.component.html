<div class="content flex items-stretch">
	<kendo-stacklayout [gap]="5" orientation="horizontal">
		<div class="ribbonButtonGroup">
			<div class="flex">
				<vs-button kendoButton shape="square" fillMode="clear" class="ribbonBigButton" (clickEvent)="save()" kendoTooltip
					class="bottom" position="bottom" [tooltipTemplate]="saveTemplate"
					[title]="'translations.operational.areas.Ribbon.Tooltips.Save'|translate"
					[disabled]="!c2oaEdit ">
					<div class="flex-column">
						<div class="inline-grid">
							<img src="assets/visuals/save.svg" style="height: 32px; width: 32px;">
						</div>
						<h5>{{"translations.operational.areas.Ribbon.Save"|translate}}</h5>
					</div>
				</vs-button>

				<vs-button kendoButton shape="square" fillMode="clear" class="ribbonBigButton" (clickEvent)="new()" kendoTooltip class="bottom"
					position="bottom" [tooltipTemplate]="newTemplate"
					[title]="'translations.operational.areas.Ribbon.Tooltips.New'|translate">
					<div class="flex-column">
						<div class="inline-grid">
							<img src="assets/visuals/Images/newC2OA.png" style="height: 32px; width: 32px;">
						</div>
						<h5>{{"translations.operational.areas.Ribbon.New"|translate}}</h5>
					</div>
				</vs-button>

				<vs-button kendoButton shape="square" fillMode="clear" class="ribbonBigButton" (clickEvent)="refresh()" kendoTooltip class="bottom"
					position="bottom" [tooltipTemplate]="refreshTemplate"
					[title]="'translations.operational.areas.Ribbon.Tooltips.Refresh'|translate">
					<div class="flex-column">
						<div class="inline-grid">
							<img src="assets/visuals/refresh.svg"
								style="height: 32px; width: 32px;">
						</div>
						<h5>{{"translations.operational.areas.Ribbon.Refresh"|translate}}</h5>
					</div>

				</vs-button>
				<div class="flex flex-col items-start">
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="remove()" kendoTooltip class="bottom"
						position="bottom" [tooltipTemplate]="deleteTemplate"
						[title]="'translations.operational.areas.Ribbon.Tooltips.Delete'|translate"
						[disabled]="(!c2oaInEdit || !approvalStatus.isDraft)"
						imageUrl="assets/visuals/Images/remove_16.png" [disabled]="true">
						{{"translations.operational.areas.Ribbon.Delete"|translate}}
					</vs-button>
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="duplicate()" kendoTooltip class="bottom"
						position="bottom" [tooltipTemplate]="duplicateTemplate"
						[title]="'translations.operational.areas.Ribbon.Tooltips.Duplicate'|translate"
						[disabled]="!c2oaInEdit"
						imageUrl="assets/visuals/duplicate.svg">
						{{"translations.operational.areas.Ribbon.Duplicate"|translate}}
					</vs-button>
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="history()" [disabled]="!c2oaInEdit"
						[title]="'translations.operational.areas.Ribbon.Tooltips.History'|translate" kendoTooltip
						class="bottom" position="bottom" [tooltipTemplate]="historyTemplate"
						imageUrl="assets/visuals/history.svg" [disabled]="true">
						{{"translations.operational.areas.Ribbon.History"|translate}}
					</vs-button>
				</div>
			</div>
			<div class="ribbonGroupTitle">C2OA</div>
		</div>
		<div class="ribbonGroupSeperator"></div>
		<div class="ribbonButtonGroup">
			<div class="flex">
				<div class="flex flex-col items-start">
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="status(approvalStatuses.Submitted)"
						[title]="'translations.operational.areas.Ribbon.Tooltips.Submit'|translate" kendoTooltip
						class="bottom" position="bottom" [tooltipTemplate]="submitTemplate"
						[disabled]="!c2oaInEdit || isStatusChangeDisabled || !approvalStatus.isDraft"
						imageUrl="assets/visuals/Images/submit_16.png">
						{{"translations.operational.areas.Ribbon.Submit"|translate}}
					</vs-button>
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="status(approvalStatuses.Approved)"
						[title]="'translations.operational.areas.Ribbon.Tooltips.Approve'|translate" kendoTooltip
						class="bottom" position="bottom" [tooltipTemplate]="approveTemplate"
						[disabled]="!c2oaInEdit || isStatusChangeDisabled  || !approvalStatus.isSubmitted"
						imageUrl="assets/visuals/Images/approve_Doc_16.png">
						{{"translations.operational.areas.Ribbon.Approve"|translate}}
					</vs-button>
					<vs-button [isRibbon]="true" [isBig]="false" (clickEvent)="status(approvalStatuses.Draft)"
						[title]="'translations.operational.areas.Ribbon.Tooltips.Reject'|translate" kendoTooltip
						class="bottom" position="bottom" [tooltipTemplate]="rejectTemplate"
						[disabled]="!c2oaInEdit || isStatusChangeDisabled || approvalStatus.isDraft"
						imageUrl="assets/visuals/Images/document_error_16.png">
						{{"translations.operational.areas.Ribbon.Reject"|translate}}
					</vs-button>
				</div>
			</div>

			<div class="ribbonGroupTitle">
				{{"translations.operational.areas.Ribbon.Status"|translate}}
			</div>
		</div>
		<div class="ribbonGroupSeperator"></div>

	</kendo-stacklayout>
</div>


<ng-template #saveTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Save" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Save" | translate}}
		</p>
	</div>
</ng-template>


<ng-template #newTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.New" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.New" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #refreshTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Refresh" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Refresh" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #deleteTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Delete" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Delete" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #duplicateTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Duplicate" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Duplicate" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #historyTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.History" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.History" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #submitTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Submit" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Submit" | translate}}
		</p>
	</div>
</ng-template>


<ng-template #approveTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Approve" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Approve" | translate}}
		</p>
	</div>
</ng-template>

<ng-template #rejectTemplate>
	<div class="tooltip">
		<p class="k-tooltip-title">{{"translations.operational.areas.Ribbon.Reject" | translate}}</p>
		<p class="k-tooltip-content">{{"translations.operational.areas.Ribbon.Tooltips.Reject" | translate}}
		</p>
	</div>
</ng-template>