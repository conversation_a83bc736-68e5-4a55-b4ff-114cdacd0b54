/* eslint-disable @typescript-eslint/naming-convention */

import * as LoadedModulesSelector from '../../../shared/store/selectors/loaded-modules.selector';
import * as UIVisibilitySelector from '../../../shared/store/selectors/ui-visibility.selector';

import { AppState } from '../../../shared/store/states/app.state';
import { C2oaService } from '../services/operational-areas.service';
import { Component } from '@angular/core';
import { ModuleNameEnum } from '../../../enums/module-name.enum';
import { ObjectHelper } from '@airc2is/aus';
import { Store } from '@ngrx/store';

/**
 * C2oa module's side panel.
 */
@Component({
	selector: 'app-c2oa-side-panel',
	templateUrl: './operational-areas-side-panel.component.html',
	styleUrls: ['./operational-areas-side-panel.component.css']
})
export class C2oaSidePanelComponent {
	public types = this._c2oaService.Types;
	public selectedType: undefined | string;
	public activeModuleName$;
	public isAllSelected = false;
	showRibbonTabContent = true;
	public svgStyle = {
		'width.px': 16,
		'height.px': 20,
		fill: '#6CC316'
	};

	constructor(public _c2oaService: C2oaService, private _store: Store<AppState>) {
		this.activeModuleName$ = this._store.select(LoadedModulesSelector.getActiveModuleName);
		this._c2oaService.listen.type.subscribe((type) => {
			this.selectedType = type;
			if (this.selectedType === 'All') {
				this.isAllSelected = true;
				return;
			}
			this.isAllSelected = false;
		});
		this._store.select(UIVisibilitySelector.getShowRibbonTabContent).subscribe((value: boolean) => {
			this.showRibbonTabContent = value;
		});
	}

	/**
	 * Send refresh event with the selected type.
	 *
	 * @param type String.
	 */
	public select(type: string): void {
		this._c2oaService.send.refresh.next(type);
	}

	/**
	 * Handles keydown events on the targeted element.
	 *
	 * @param {KeyboardEvent} event - The KeyboardEvent object representing the key that was pressed.
	 */
	public keydown(event: KeyboardEvent): void {
		if (event.key === 'ArrowUp') {
			this._prev();
		} else if (event.key === 'ArrowDown') {
			this._next();
		}
	}

	/**
	 * Gets module name enums.
	 *
	 * @returns Module name.
	 */
	public getModuleNameEnum(): typeof ModuleNameEnum {
		return ModuleNameEnum;
	}

	/**
	 * Current index the selected type.
	 *
	 * @returns Number.
	 */
	private _currentIndex(): number {
		if (!this.selectedType) {
			return -1;
		}
		return this.types.findIndex(t => t.shortName === this.selectedType);
	}

	/**
	 * Prev type of the selected type.
	 */
	private _prev(): void {
		const currentIndex = this._currentIndex();
		if (currentIndex >= 0) {
			this.select(this.types[currentIndex - 1]?.shortName);
		}
	}

	/**
	 * Next type of the selected type.
	 */
	private _next(): void {
		const currentIndex = this._currentIndex();
		const type = this.types[currentIndex + 1];
		if (type) {
			this.select(type.shortName);
		}
	}
}
