import { ActiveTimePeriodObject, C2oa } from 'src/app/types/operational-areas';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { DialogLevelEnum, MessageUtility, RadioButtonType } from '@airc2is/vscontrols';

import { ActivityStatusEnum } from '../../../services/enum/activity-status.enum';
import { C2oaService } from '../../../services/operational-areas.service';
import { Guid } from 'guid-typescript';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';

/**
 *
 */
type FromToRadioButtonType = RadioButtonType & { isDefault?: boolean };
const translatePrefix = 'translations.operational.areas.Details.Time.';
/**
 *
 */
@Component({
	selector: 'app-c2oa-time',
	templateUrl: './operational-areas-time.component.html',
	styles: [`
		vs-datetimepicker ::ng-deep > div { flex-wrap: wrap; }
		vs-datetimepicker ::ng-deep kendo-datetimepicker { min-width:100%; }
		vs-datetimepicker ::ng-deep kendo-combobox { display:none;}
		vs-radiobutton ::ng-deep kendo-buttongroup { display:flex; }
		vs-radiobutton#fromToRadioBtns ::ng-deep kendo-buttongroup button:last-child { margin-left:auto; margin-inline-start:auto !important; }
	`]
})
export class C2oaTimeComponent implements OnChanges {
	@Input() model: C2oa = <C2oa>{};
	@Output() modelChange = new EventEmitter();

	@Input() isReadonly = true;

	public isActiveOnPeriod = false;
	public isRangePeriod = true;
	public isDisabled = false;

	public statusRadioBtns: Array<RadioButtonType> = [
		{
			name: ActivityStatusEnum.ActiveAlways,
			tooltip: this._translate.instant(`${translatePrefix}Status.AlwaysActive`),
			label: this._translate.instant(`${translatePrefix}Status.AlwaysActive`),
			isSelected: true
		}, {
			name: ActivityStatusEnum.Dormant,
			tooltip: this._translate.instant(`${translatePrefix}Status.Dormant`),
			label: this._translate.instant(`${translatePrefix}Status.Dormant`),
			isSelected: false
		},
		{
			name: ActivityStatusEnum.ActiveOnPeriod,
			tooltip: this._translate.instant(`${translatePrefix}Status.ActiveOnPeriod`),
			label: this._translate.instant(`${translatePrefix}Status.ActiveOnPeriod`),
			isSelected: false
		}
	];

	public fromToRadioBtns: Array<FromToRadioButtonType> = [
		{
			label: this._translate.instant(`${translatePrefix}To`),
			isSelected: true,
			isDefault: true
		},
		{
			label: this._translate.instant(`${translatePrefix}Unknown`),
			isSelected: false
		}
	];

	public dates = {
		from: new Date(),
		to: new Date()
	};

	public warning = {
		isVisible: false,
		content: ''
	};

	public removeWarning = {
		isVisible: false,
		content: '',
		title: '',
		closeCallback: (canContinue: boolean): void => console.error(`No callback assigned ${canContinue}`)
	};

	public effectiveTimeData: ActiveTimePeriodObject[] = [];

	private _activityStatus: ActivityStatusEnum = ActivityStatusEnum.ActiveAlways;

	private _warnings = {
		toFromEarlier: this._translate.instant(`${translatePrefix}Warnings.ToFromEarlier`),
		overlap: this._translate.instant(`${translatePrefix}Warnings.Overlap`),
		deletedPeriod: this._translate.instant(`${translatePrefix}Warnings.DeletedPeriod`),
		deleteAllPeriods: this._translate.instant(`${translatePrefix}Warnings.DeleteAllPeriods`),
		mandatory: this._translate.instant(`${translatePrefix}Warnings.Mandatory`)
	};

	private _disableForTypes = this._c2oaService.Types.filter(type => type.disabledActiveStatusChange).map((type) => type.shortName);

	constructor(private _c2oaService: C2oaService, private _translate: TranslateService) {
		//
	}

	/**
	 * Ng on changes.
	 *
	 * @param changes SimpleChanges.
	 */
	ngOnChanges(changes: SimpleChanges): void {
		const activityStatus = changes.model?.currentValue?.activityStatus;
		const activityStatusChanged =
			changes.model?.currentValue && activityStatus !== this._activityStatus;

		if (activityStatusChanged) {
			this._activityStatus = ActivityStatusEnum.ActiveAlways;
			this.isActiveOnPeriod = false;

			this.statusRadioBtns.forEach((status) => {
				status.isSelected = activityStatus === status.name;

				if (status.isSelected && status.name) {
					this._activityStatus = status.name as ActivityStatusEnum;
					this.isActiveOnPeriod = status.name === ActivityStatusEnum.ActiveOnPeriod;
				}
			});

			this.fromToRadioBtns.forEach((item) => (item.isSelected = item.isDefault || false));
			this.isRangePeriod = true;
		}

		this.effectiveTimeData = changes.model?.currentValue?.timePeriods || [];
		this.isDisabled = this._disableForTypes.includes(this.model?.c2oaType);

		if (this.isDisabled) {
			this.effectiveTimeData = [];
			this.statusRadioBtns[0].isSelected = true;
			this.statusClick(this.statusRadioBtns[0], true);
		}
	}

	/**
	 * Status Click Event.
	 *
	 * @param option RadioButtonType.
	 * @param skipWarning Boolean.
	 */
	statusClick(option: RadioButtonType, skipWarning = false): void {
		const removeAllPeriods = this.isActiveOnPeriod && option.name !== ActivityStatusEnum.ActiveOnPeriod && this.effectiveTimeData.length;

		if (!skipWarning && removeAllPeriods) {
			this.removeAll(false, option);
		} else {
			this._activityStatus = option.name as ActivityStatusEnum;
			this.isActiveOnPeriod = option.name === ActivityStatusEnum.ActiveOnPeriod;

			this.statusRadioBtns.forEach((opt) => (opt.isSelected = opt.name === option.name));

			this._modelChange();
		}
	}

	/**
	 * FromTo Click Event.
	 *
	 * @param option FromToRadioButtonType.
	 */
	fromToClick(option: FromToRadioButtonType): void {
		this.isRangePeriod = option.isDefault || false;
		// TODO: radiobuttons design & isreadonly is not working
		this.dates.to = this.isRangePeriod ? new Date() : new Date(new Date('9999-12-31:23:59:59').toISOString().slice(0, -1));
	}

	/**
	 * Add time period.
	 */
	add(): void {
		//
		if (this.isActiveOnPeriod && this._validDates()) {
			//
			this.effectiveTimeData.push(
				<ActiveTimePeriodObject>{
					fromDate: this.dates.from,
					toDate: this.dates.to,
					id: Guid.create().toString()
				});

			this._modelChange();
		}
		this.effectiveTimeData = cloneDeep(this.effectiveTimeData);
	}

	/**
	 * Delete time period.
	 *
	 * @param period C2oaActiveTimePeriod.
	 * @param skipWarning Boolean.
	 * @param canRemove Boolean.
	 */
	remove(period: ActiveTimePeriodObject, skipWarning = false, canRemove = false): void {
		if (!skipWarning) {
			this.removeWarning.content = this._warnings.deletedPeriod;
			this.removeWarning.title = this._translate.instant(`${translatePrefix}Delete`);
			this.removeWarning.closeCallback = this.remove.bind(this, period, true);
			this.removeWarning.isVisible = true;
			this._openRemoveWarningDialog();
		} else {
			this.removeWarning.isVisible = false;

			if (canRemove) {
				const indexToRemove = this.effectiveTimeData.findIndex(tp => `${period.toDate}${period.fromDate}` === `${tp.toDate}${tp.fromDate}`);
				this.effectiveTimeData.splice(indexToRemove, 1);

				this._modelChange();
			}
		}
		this.effectiveTimeData = cloneDeep(this.effectiveTimeData);
	}

	/**
	 * Delete all time periods.
	 *
	 * @param skipWarning Boolean.
	 * @param option RadioButtonType.
	 */
	removeAll(skipWarning: boolean, option: RadioButtonType): void {
		if (!skipWarning) {
			this.removeWarning.content = this._warnings.deleteAllPeriods;
			this.removeWarning.title = this._translate.instant(`${translatePrefix}DeleteAll`);

			this.removeWarning.closeCallback =
				(canRemove: boolean): void => {
					if (canRemove) {
						this.removeAll(true, option);
						this.statusClick(option, true);
					} else {
						const revertOption = this.statusRadioBtns.find(opt => (opt.name === this._activityStatus));
						if (revertOption) {
							this.statusClick(revertOption, true);
						}
					}
					this.removeWarning.isVisible = false;
				};
			this.removeWarning.isVisible = true;
			this._openRemoveWarningDialog();
		} else {
			//
			this.removeWarning.isVisible = false;
			this.effectiveTimeData = [];
			this._modelChange();
		}
	}

	/**
	 * Emit the changes.
	 */
	private _modelChange(): void {
		this.model.timePeriods = [...this.effectiveTimeData];
		this.model.activityStatus = this._activityStatus;

		this.modelChange.emit(this.model);
	}

	/**
	 * Validate the dates.
	 *
	 * @returns Boolean The result.
	 */
	private _validDates(): boolean {
		const hasFrom = this.fromToRadioBtns.find(btn => btn.isDefault);
		const toTime = (new Date(this.dates.to)).getTime();
		const fromTime = (new Date(this.dates.from)).getTime();

		if (hasFrom?.isSelected) {
			if (toTime <= fromTime) {
				this.warning.content = this._warnings.toFromEarlier;
				this.warning.isVisible = true;
				this._openWarningDialog();

				return false;
			}
		}

		const higherPeriod = this.effectiveTimeData
			.find(tp => (new Date(tp.toDate)).getTime() >= fromTime);

		if (higherPeriod) {
			this.warning.content = this._warnings.overlap;
			this.warning.isVisible = true;
			this._openWarningDialog();
			return false;
		}

		return true;
	}

	/**
	 * Open warning dialog.
	 */
	private _openWarningDialog(): void {
		MessageUtility.showMessage({
			title: this._translate.instant('translations.shared.Information'),
			content: this.warning.content,
			level: DialogLevelEnum.Info,
			helpParameter: '#help_param', // TODO: replace help param
			buttonText: this._translate.instant('translations.shared.Close'),
			width: 450,
			height: 200
		});
	}

	/**
	 * Open remove warning dialog.
	 */
	private _openRemoveWarningDialog(): void {
		MessageUtility.showConfirmation({
			title: this.removeWarning.title,
			content: this.removeWarning.content,
			submitText: this._translate.instant('translations.shared.Continue'),
			cancelText: this._translate.instant('translations.shared.Cancel'),
			width: 450,
			height: 200
		}).subscribe((result) => {
			if (result) {
				this.removeWarning.closeCallback(true);
			}
		});
	}
}
