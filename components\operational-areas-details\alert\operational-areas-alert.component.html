<kendo-gridlayout [gap]="15" [rows]="[120]" [cols]="['40%', '40%']" class="mt-3 flex items-center" *ngIf="model">

    <kendo-gridlayout-item [row]="1">
        <vs-checkbox [label]="'translations.operational.areas.Details.Alert.EnableAlert'|translate"
            [title]="'translations.operational.areas.Details.Tooltips.EnableAlert'|translate" class="flex gap-x-2.5"
            [(ngModel)]="model.alertEnabled" (checkedChange)="onChange()" [disabled]="isReadonly || model.isDisabled">
        </vs-checkbox>
    </kendo-gridlayout-item>

    <kendo-gridlayout-item [row]="1">

        <kendo-gridlayout [gap]="15" [rows]="[60, 60]" [cols]="['48%', '48%']">

            <kendo-gridlayout-item [row]="1">
                <vs-dropdownlist [data]="affiliations" textField="key" valueField="key" (valueChange)="onChange()"
                    [readonly]="isDisabled || isReadonly || model.isDisabled"
                    [title]="'translations.operational.areas.Details.Tooltips.Affiliation'|translate"
                    [label]="'translations.operational.areas.Details.Alert.Affiliation'|translate"
                    [(ngModel)]="affiliation">
                </vs-dropdownlist>
            </kendo-gridlayout-item>

            <kendo-gridlayout-item [row]="1">
                <vs-dropdownlist [data]="unitTypes" textField="key" valueField="key" (valueChange)="onChange()"
                    [label]="'translations.operational.areas.Details.Alert.UnitType'|translate"
                    [title]="'translations.operational.areas.Details.Tooltips.UnitType'|translate"
                    [readonly]="isDisabled || isReadonly || model.isDisabled" [(ngModel)]="objectType">
                </vs-dropdownlist>
            </kendo-gridlayout-item>

            <kendo-gridlayout-item [row]="2" class="flex items-end">
                <vs-unitcontrol 
                    [unitControlValue]="coverage.value" [readonly]="isDisabled || isReadonly || model.isDisabled"
                    [unitControlType]="distanceType"
                    [label]="'translations.operational.areas.Details.Alert.CombatRange'|translate"
                    [title]="'translations.operational.areas.Details.Tooltips.CombatRange'|translate"
                    (unitControlValueChange)="coverageChange($event)">
                </vs-unitcontrol>
            </kendo-gridlayout-item>

            <kendo-gridlayout-item [row]="2" class="flex items-end">
                <vs-checkbox [label]="'translations.operational.areas.Details.Alert.ConsiderPlannedLocation'|translate"
                    [title]="'translations.operational.areas.Details.Tooltips.ConsiderPlannedLocation'|translate"
                    class="flex gap-x-2.5" [(ngModel)]="model.isPlanned" (checkedChange)="onChange()"
                    [disabled]="isDisabled || isReadonly || model.isDisabled">
                </vs-checkbox>
            </kendo-gridlayout-item>

        </kendo-gridlayout>

    </kendo-gridlayout-item>

</kendo-gridlayout>