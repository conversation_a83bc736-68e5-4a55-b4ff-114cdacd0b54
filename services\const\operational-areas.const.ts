import { C2OTypesType } from '../enum/c20a-types.enum';
import { C2oaType } from 'src/app/types/operational-areas/operational-areas-type.type';

const c2oaTypesIcon = 'assets/visuals/Images/operationalAreas.png';

export const c2oaTypes: C2oaType[] = [
	{
		name: 'Joint Operations Area',
		shortName: C2OTypesType.JOA,
		icon: c2oaTypesIcon,
		disabledStatusChange: true,
		disabledActiveStatusChange: true
	},
	{
		name: 'Area of Operational Responsability',
		shortName: C2OTypesType.AOR,
		icon: c2oaTypesIcon
	},
	{
		name: 'Joint Fire Support Coordination Line',
		shortName: C2OTypesType.JFSCL,
		icon: c2oaTypesIcon
	},
	{
		name: 'Fire Support Coordination Measure',
		shortName: C2OTypesType.FSCM,
		icon: c2oaTypesIcon
	},
	{
		name: 'Fire Support Safety Line',
		shortName: C2OTypesType.FSSL,
		icon: c2oaTypesIcon
	},
	{
		name: 'Forward Line of Own Troops',
		shortName: C2OTypesType.FLOT,
		icon: c2oaTypesIcon
	},
	{
		name: 'Position and Intended Movement',
		shortName: C2OTypesType.PIM,
		icon: c2oaTypesIcon
	},
	{
		name: 'Deployment Aread of a TBMD Defence Resource',
		shortName: C2OTypesType.DAofTBMD,
		icon: c2oaTypesIcon
	},
	{
		name: 'Threat Operation Area',
		shortName: C2OTypesType.TOA,
		icon: c2oaTypesIcon
	},
	{
		name: 'Threat Targeting Area',
		shortName: C2OTypesType.TTA,
		icon: c2oaTypesIcon
	},
	{
		name: 'Kill Box',
		shortName: C2OTypesType.KB,
		icon: c2oaTypesIcon
	},
	{
		name: 'Restricted Operating Zone',
		shortName: C2OTypesType.ROZ,
		icon: c2oaTypesIcon
	},
	{
		name: 'Missile Engagement Zone',
		shortName: C2OTypesType.MEZ,
		icon: c2oaTypesIcon
	},
	{
		name: 'Generic',
		shortName: C2OTypesType.Generic,
		icon: c2oaTypesIcon
	}
];
