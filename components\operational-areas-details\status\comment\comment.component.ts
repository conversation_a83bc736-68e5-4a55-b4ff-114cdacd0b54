import { Component, OnD<PERSON>roy } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

/**
 *
 */
@Component({
	selector: 'app-comment',
	templateUrl: './comment.component.html',
	styleUrls: []
})
export class CommentComponent implements OnDestroy {
	statusCommentForm!: FormGroup;
	label = this._translate.instant('translations.operational.areas.Ribbon.StatusChange.Comment');

	latestCommentValue: string | null = null;
	private _subscriptions: Subscription[] = [];

	constructor(private _translate: TranslateService) {
		// Generate comment form control name
		this.statusCommentForm = new FormGroup({ comment: new FormControl('') });

		this.statusCommentForm?.get('comment')?.valueChanges.subscribe(value => {
			this.latestCommentValue = value;
		});
	}

	/**
	 * Ng on destroy.
	 */
	ngOnDestroy(): void {
		this._subscriptions.forEach((i) => i.unsubscribe());
	}
}
