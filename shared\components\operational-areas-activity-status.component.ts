/* eslint-disable dot-notation */
/* eslint-disable sort-imports */
import { Component, Input } from '@angular/core';
import { ActiveTimePeriodObject } from 'src/app/types/operational-areas';
import { ActivityStatusEnum } from '../../services/enum/activity-status.enum';
import { TranslateService } from '@ngx-translate/core';

const translatePrefix = `translations.operational.areas.Shared.ActivityStatus.`;
/**
 *
 */
@Component({
	selector: 'app-c2oa-activity-status',
	template: `
		<svg-icon src="assets/visuals/clock.svg"
			[svgStyle]="{ 'width.px': width, 'height.px': height, 'fill': fill }" [title]="label">
		</svg-icon>
		<strong class="uppercase" [style]="{'color': fill}" [innerText]='label' *ngIf="!hideLabel" [title]="tooltip"></strong>
	`,
	styles: [`
		:host { display: flex; align-items: center; gap: 10px; }
	`]
})
export class C2oaActivityStatusComponent {
	@Input() hideLabel = false;
	@Input() width = 18;
	@Input() height = 18;

	public fill = '';
	public label = '';
	public tooltip = this._translate.instant(`${translatePrefix}Tooltip`);

	private _timePeriods: ActiveTimePeriodObject[] = [];
	private _activityStatus: ActivityStatusEnum | string = ActivityStatusEnum.ActiveAlways;

	private readonly _activityStatuses: { [x: string]: { fill: string, label: string } | { [x: string]: { fill: string, label: string } } } = {
		[ActivityStatusEnum.ActiveAlways]: {
			fill: '#6CC316',
			label: this._translate.instant(`${translatePrefix}Active`)
		},
		[ActivityStatusEnum.Dormant]: {
			fill: '#8c8c8c',
			label: this._translate.instant(`${translatePrefix}Dormant`)
		},
		// TODO: this is not right, active on period status display/label is based on period
		[ActivityStatusEnum.ActiveOnPeriod]: {
			waiting: {
				fill: '#dfc269fa',
				label: this._translate.instant(`${translatePrefix}Waiting`)
			},
			active: {
				fill: '#6CC316',
				label: this._translate.instant(`${translatePrefix}Active`)
			},
			dormant: {
				fill: '#8c8c8c',
				label: this._translate.instant(`${translatePrefix}Dormant`)
			}
		}
	};

	constructor(private _translate: TranslateService) {
		//
	}

	/**
	 * Condition.
	 */
	// eslint-disable-next-line accessor-pairs, no-restricted-syntax
	@Input() set timePeriods(value: ActiveTimePeriodObject[]) {
		this._timePeriods = value;
		this._transform();
	}

	/**
	 * Condition.
	 */
	// eslint-disable-next-line accessor-pairs, no-restricted-syntax
	@Input() set activityStatus(value: keyof typeof ActivityStatusEnum) {
		this._activityStatus = ActivityStatusEnum[value as keyof typeof ActivityStatusEnum];
		this._transform();
	}

	/**
	 * Condition.
	 */
	private _transform(): void {
		if (this._activityStatus) {
			//
			if (this._activityStatus !== ActivityStatusEnum.ActiveOnPeriod) {
				this.fill = this._activityStatuses[this._activityStatus].fill as string;
				this.label = this._activityStatuses[this._activityStatus].label as string;
			} else {
				//
				const status = this._activityStatuses[this._activityStatus] as { [x: string]: { fill: string, label: string } };

				if (!this._timePeriods.length) {
					this.fill = status['waiting'].fill;
					this.label = status['waiting'].label;
				} else {
					// ActivityStatus[model.activityStatus].fill
					const timeData = this._timePeriods
						.map(tp => ({
							toTime: (tp.toDate instanceof Date) ? tp.toDate.getTime() : (new Date(tp.toDate).getTime()),
							fromTime: (tp.fromDate instanceof Date) ? tp.fromDate.getTime() : (new Date(tp.fromDate).getTime())
						}));

					const maxDate = Math.max(...timeData.map(tp => tp.toTime));

					const currentTime = new Date().getTime();
					const isActive = timeData.find(tp => tp.fromTime <= currentTime && tp.toTime >= currentTime);

					// eslint-disable-next-line no-nested-ternary
					const actuallyStatus = isActive ? status['active'] : ((maxDate > currentTime) ? status['waiting'] : status['dormant']);

					this.fill = actuallyStatus.fill;
					this.label = actuallyStatus.label;
				}
			}
		} else {
			this.fill = '';
			this.label = '';
		}
	}
}
