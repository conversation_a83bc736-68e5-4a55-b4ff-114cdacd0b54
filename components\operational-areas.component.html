<div class="c2oaContentContainer" [hidden]="isCollapsed">

	<div class="flex w-full">
		<div class="tbmd headerBar w-full">
			<div class="opacity-50 mr-2">{{ type }}</div>
			<div class="cursor-pointer">
				<svg-icon [src]="
						isCollapsed
							? '../../assets/visuals/chevron_right.svg'
							: '../../assets/visuals/chevron_left.svg'
					" [svgStyle]="{
						'height.px': 10,'margin-right.px':10,
						fill: '#FFFFFF'
					}" (click)="changeCollapsed()">
				</svg-icon>
			</div>
		</div>
	</div>
	<div class="formArea h-full gridContent flex">
			<kendo-splitter orientation="vertical" class="contentSplitter h-full">
				<kendo-splitter-pane class="text-white" [scrollable]="true">
					<div class="flex flex-col h-full flex-grow" kendoTooltip>
						<vs-matrixview class="c2oa-table" [selectedKeyName]="selectedKeyName"  [rowHeight]="20" 
							[selectedKeys]="selectedKeys" [selectableMode]="'single'" [selectFirstRow]="false"
							[data]="data" (selectionChangeEvent)="onTableRowSelect($any($event))" [resizable]="true"
							[reorderable]="true" (rightClick)="onRightClick($event)" [scrollable]="'virtual'"
							style="overflow: auto"
							[gridTitle]="'translations.operational.areas.Grid.Title'|translate">

							<kendo-grid-column title="#" [headerStyle]="{'width': '5%'}" [style]="{'width': '5%'}"
								[includeInChooser]="false">
								<ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
									{{rowIndex+1}}
								</ng-template>
							</kendo-grid-column>

							<kendo-grid-column field="none" title="!" [width]="50" [includeInChooser]="false">
								<ng-template kendoGridHeaderTemplate>
									<span
										[title]="'translations.operational.areas.Grid.Tooltips.SyncStatus'|translate">!</span>
								</ng-template>
							</kendo-grid-column>

							<kendo-grid-column field="IsVisibleOnMap" title="Visible on map" [width]="50"
								[includeInChooser]="false">
								<ng-template kendoGridHeaderTemplate>
									<svg-icon src="assets/visuals/TID/eye.svg"
										[title]="'translations.operational.areas.Grid.Tooltips.MapVisibility'|translate"
										(click)="toggleMapVisibilityAll($event)"
										[svgStyle]="{ 'width.px': 16, 'height.px': 20, fill: isVisibleOnMapAll ? '#dddddd' : '#8c8c8c'  }">
									</svg-icon>
								</ng-template>
								<ng-template kendoGridCellTemplate let-dataItem>
									<svg-icon (click)="toggleMapVisibility($event, dataItem)"
										[title]="'translations.operational.areas.Grid.Tooltips.MapVisibilityColumn'|translate"
										src="assets/visuals/TID/eye.svg" [svgStyle]="{ 'width.px': 16, 'height.px': 20, 
										fill: dataItem.isVisibleOnMap ? '#dddddd' : '#8c8c8c' }">
									</svg-icon>
								</ng-template>
							</kendo-grid-column>
							<kendo-grid-column field="activityStatus" title="Activity Status" [width]="50">
								<ng-template kendoGridHeaderTemplate>
									<svg-icon src="assets/visuals/clock.svg"
										[title]="'translations.operational.areas.Grid.Tooltips.ActivityStatus'|translate"
										[svgStyle]="{ 'width.px': 16, 'height.px': 20, fill: '#8c8c8c' }">
									</svg-icon>
								</ng-template>
								<ng-template kendoGridCellTemplate let-dataItem>
									<app-c2oa-activity-status [activityStatus]="dataItem.activityStatus"
										[hideLabel]="true" [width]="16" [height]="20"
										[timePeriods]="dataItem.timePeriods"></app-c2oa-activity-status>
								</ng-template>
							</kendo-grid-column>
							<kendo-grid-column field="c2oaName" title="Name" [width]="120">
								<ng-template kendoGridHeaderTemplate let-dataItem>
									<span [title]="'translations.operational.areas.Grid.Tooltips.Name'|translate">
										{{'translations.operational.areas.Grid.Columns.Name'|translate}}
									</span>
								</ng-template>
							</kendo-grid-column>
							<kendo-grid-column field="c2oaType" title="Type" [width]="80">
								<ng-template kendoGridHeaderTemplate let-dataItem>
									<span [title]="'translations.operational.areas.Grid.Tooltips.Type'|translate">
										{{'translations.operational.areas.Grid.Columns.Type'|translate}}
									</span>
								</ng-template>
							</kendo-grid-column>


							<kendo-grid-column field="altitudeMax" title="Altitude" [width]="120" [hidden]="true"
								[columnMenu]="false">
								<ng-template kendoGridHeaderTemplate let-dataItem>
									<span [title]="'translations.operational.areas.Grid.Tooltips.Altitude'|translate">
										{{'translations.operational.areas.Grid.Columns.Altitude'|translate}}
									</span>
								</ng-template>

								<ng-template kendoGridCellTemplate let-dataItem>
									{{ dataItem.altitudeMin }} - {{ dataItem.altitudeMax }}
								</ng-template>
							</kendo-grid-column>

							<kendo-grid-column field="shape" title="Shape" [width]="50">
								<ng-template kendoGridHeaderTemplate let-dataItem>
									<span [title]="'translations.operational.areas.Grid.Tooltips.Shape'|translate">
										{{'translations.operational.areas.Grid.Columns.Shape'|translate}}
									</span>
								</ng-template>
								<ng-template kendoGridCellTemplate let-dataItem>
									<div [title]="dataItem.shape" class="shapeImage"
										[ngStyle]="{'background-image': 'url('+dataItem.shapeSvgUrl+')'}"></div>
								</ng-template>
							</kendo-grid-column>

							<kendo-grid-column field="approvalStatus" title="Approval Status" [width]="70">
								<ng-template kendoGridHeaderTemplate let-dataItem>
									<span [title]="'translations.operational.areas.Grid.Tooltips.Status'|translate">
										{{'translations.operational.areas.Grid.Columns.Status'|translate}}
									</span>
								</ng-template>
								<ng-template kendoGridCellTemplate let-dataItem>
									<img [src]="dataItem.approvalStatusSvgUrl" style="height: 16px; width: 16px;"
										[title]="('translations.operational.areas.Shared.ApprovalStatus.' + dataItem.approvalStatus)|translate" />

								</ng-template>
							</kendo-grid-column>
						</vs-matrixview>
					</div>

				</kendo-splitter-pane>

				<kendo-contextmenu [items]="contextMenuItems" #contextMenu (select)="onContextMenuSelect($event)">
					<ng-template kendoMenuItemTemplate let-item="item">
						<div class="flex">
							<img [src]="item.iconUrl">
							<span style="padding-left: 15px">{{ item.text }} </span>
						</div>
					</ng-template>
				</kendo-contextmenu>

				<kendo-splitter-pane min="5%" [size]="detailsPanel.splitterSize" [resizable]="false"
					[max]="detailsPanel.maxSplitterSize" [scrollable]="false">

					<kendo-expansionpanel [animation]="false" [expanded]="detailsPanel.expanded"
						class="vsexpandpanel firstLevelHeader"
						[subtitle]="detailsPanel.subtitle + detailsPanel.c2oaName" (expand)="toggleDetails(true)"
						(collapse)="toggleDetails(false)">
					</kendo-expansionpanel>

					<div class="sidePanelItemOverview pr-2 text-white">
						{{ detailsPanel.item | json }}
						<app-c2oa-details (nameChange)="setDetailsC2oaName($any($event))"
							(lastSelectedValue)="onLastSelectedValue($event)" 
							(selectedKey)="onSelectedKey($event)"
							[model]="detailsPanel.item"></app-c2oa-details>
					</div>
				</kendo-splitter-pane>
			</kendo-splitter>

		<app-report-viewer-dialog [items]="entity" [show]="show"
			(windowClose)="show = $event"></app-report-viewer-dialog>
	</div>
</div>
<div *ngIf="isCollapsed" (click)="changeCollapsed()" class="orbatPageLayout orbatContentContainerMini collapsedSideBar">
	<svg-icon src="../../assets/visuals/chevron_right.svg" [svgStyle]="{
			'margin-top.px': 10,
			'height.px': 10,
			fill: '#FFFFFF'
		}" (click)="changeCollapsed()">
	</svg-icon>
	<div class="collapsedSideBarContent">
		<div class="opacity-50 mr-2">{{ activeModuleName }}</div>
	</div>
</div>