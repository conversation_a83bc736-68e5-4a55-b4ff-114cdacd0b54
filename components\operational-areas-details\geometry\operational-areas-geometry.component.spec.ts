/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oaGeometryComponent } from './operational-areas-geometry.component';
import { C2oaService } from '../../../services/operational-areas.service';
import { CoreModule } from 'src/app/core/core.module';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { provideMockStore } from '@ngrx/store/testing';


describe('C2oaGeometryComponent', () => {
	let component: C2oaGeometryComponent;
	let fixture: ComponentFixture<C2oaGeometryComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, FormsModule, CoreModule, BrowserAnimationsModule],
					declarations: [C2oaGeometryComponent],
					providers: [C2oaService, provideMockStore({})]
				});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaGeometryComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());
});
