import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { dummyId, dummyMultipleGetResponse, dummyName, dummySingleCreate, dummySingleGetResponse } from './mocks';

import { ApiEndpointConstants } from 'src/app/constants/apiendpoint.constant';
import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { C2oa } from 'src/app/types/operational-areas';
import { C2oaService } from './operational-areas.service';
import { TestBed } from '@angular/core/testing';
import { c2oaTypes } from './const/operational-areas.const';
import { provideMockStore } from '@ngrx/store/testing';

describe('C2oaService', () => {
	const rootUrl = ApiEndpointConstants.C2oaService;
	let service: C2oaService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [HttpClientTestingModule],
			providers: [C2oaService, provideMockStore({})]
		});
		service = TestBed.inject(C2oaService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('save(model:C2oa) should save and return saved item', () => {
		service.save(dummySingleCreate)
			.subscribe((res: C2oa) => expect(res.c2oaName).toEqual(dummySingleCreate.c2oaName));

		const req = httpMock.expectOne(`${rootUrl}${service.Actions.save}`);

		expect(req.request.method).toBe('POST');

		req.flush(dummySingleGetResponse);
	});

	it('getById(id:string) should return data', () => {
		service.getById(dummyId)
			.subscribe((res: C2oa) => expect(res).toBe(dummySingleGetResponse));

		const req = httpMock.expectOne(`${rootUrl}${service.Actions.getById}`);

		expect(req.request.method).toBe('POST');

		req.flush(dummySingleGetResponse);
	});

	it('getByName(name:string) should return data', () => {
		service.getByName(dummyName)
			.subscribe((res: C2oa) => expect(res).toBe(dummySingleGetResponse));

		const req = httpMock.expectOne(`${rootUrl}${service.Actions.getByName}`);

		expect(req.request.method).toBe('POST');

		req.flush(dummySingleGetResponse);
	});

	it('refresh() should return data', () => {
		service.refresh()
			.subscribe((res: C2oa[]) => expect(res.length).toBe(dummyMultipleGetResponse.length));

		const req = httpMock.expectOne(`${rootUrl}${service.Actions.getList}`);

		expect(req.request.method).toBe('GET');

		req.flush(dummyMultipleGetResponse);
	});

	it('refresh(type:string) should return data', () => {
		const type = c2oaTypes.find(type => type.shortName === 'ROZ');
		const filtered = dummyMultipleGetResponse.filter(item => item.c2oaType === type?.shortName);

		service.refresh(type?.shortName)
			.subscribe((res: C2oa[]) => expect(res.length).toBe(filtered.length));

		const req = httpMock.expectOne(`${rootUrl}${service.Actions.getListByType}`);
		req.flush(filtered);

		expect(req.request.method).toBe('POST');
		expect(service.lastSelectedType).toEqual(type?.shortName);
	});

	it('registerAnswer should register', () => {
		service.registerAnswer('isDetailsChanged', () => false);
		service.registerAnswer('isDetailsChanged', () => true);
	});

	it('ask should register', () => {
		service.registerAnswer('isDetailsChanged', () => true);
		service.ask('isDetailsChanged', () => true);
	});

	it('canChangeSelection should respond true', () => {
		service.canChangeSelection().subscribe((res: boolean) => expect(res).toBeTrue());
	});

	it('remove should call the correct endpoint and return the result', () => {
		const id = '1';

		const req = httpMock.expectOne(`${rootUrl}/${id}`);
		req.flush(id);

		expect(req.request.method).toBe('DELETE');

		service.remove(id).subscribe((result) => expect(result).toEqual(id));
	});

	it('changeStatus should call the correct endpoint and return the mapped result', () => {
		const item: C2oa = {
			id: 1,
			approvalStatus: ApprovalStatusEnum.Submitted
		} as unknown as C2oa;
		const status: ApprovalStatusEnum = ApprovalStatusEnum.Approved;
		const expectedUrl = `${rootUrl}/${service.Actions.approve}`;
		const expectedResponse: C2oa = {
			id: 1,
			approvalStatus: ApprovalStatusEnum.Approved
		} as unknown as C2oa;

		const req = httpMock.expectOne(expectedUrl);
		req.flush(expectedResponse);

		expect(req.request.method).toBe('POST');

		service.changeStatus(item, status).subscribe((result) => expect(result).toEqual(expectedResponse));
	});

	afterEach(() => {
		httpMock.verify();
	});
});
