/* eslint-disable quotes */

import { BaseService, HttpServiceObjectType, hideLoader, showLoader } from '@airc2is/aus';
import { BehaviorSubject, Observable, Observer, ReplaySubject, Subject, Subscription, map, tap } from 'rxjs';

import { ApiEndpointConstants } from '../../../constants/apiendpoint.constant';
import { AppState } from '../../../shared/store/states/app.state';
import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { C2oa } from 'src/app/types/operational-areas/operational-areas.type';
import { C2oaShapesHelper } from 'src/app/helpers/c2oa-shapes.helper';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { c2oaTypes } from './const/operational-areas.const';

/**
 *
 */
type C2oaQuestionEvent = 'isDetailsChanged';

/**
 * Own Orbat service for api call.
 */
@Injectable({ providedIn: 'root' })
export class C2oaService extends BaseService {
	public readonly Types = c2oaTypes;
	public readonly Actions = {
		getById: 'get-c2oa-by-id',
		getByName: 'get-c2oa-by-name',
		getList: 'get-c2oa-list',
		getListByType: 'get-c2oa-list-by-c2oa-type',
		save: 'save-c2oa',
		delete: 'delete-c2oa',
		approve: 'approve-c2oa',
		reject: 'reject-c2oa',
		submit: 'submit-c2oa'
	};

	public send = {

		changeSelectionWarning: new Subject<boolean>(),
		canChangeSelection: new Subject<boolean>(),

		refresh: new ReplaySubject<string>(),
		type: new ReplaySubject<string | undefined>(),
		create: new Subject<boolean>(),
		edit: new ReplaySubject<C2oa | null>(),
		save: new Subject<boolean>(),
		status: new Subject<ApprovalStatusEnum>(),
		history: new Subject<boolean>(),
		delete: new Subject<string | undefined>(),
		c20aData: new BehaviorSubject<C2oa[]>([])
	};

	public listen = {

		changeSelectionWarning: this.send.changeSelectionWarning.asObservable(),
		canChangeSelection: this.send.canChangeSelection.asObservable(),

		refresh: this.send.refresh.asObservable(),
		type: this.send.type.asObservable(),
		create: this.send.create.asObservable(),
		edit: this.send.edit.asObservable(),
		save: this.send.save.asObservable(),
		status: this.send.status.asObservable(),
		history: this.send.history.asObservable(),
		delete: this.send.delete.asObservable()
	};

	public canChangeSelectionSub: Subscription | undefined;

	private _type = '';
	private _answer: { [event in C2oaQuestionEvent]?: (callback: (data: unknown) => void) => void } = {};

	constructor(http: HttpClient, private _store: Store<AppState>) {
		super(http);
	}

	/**
	 * Get the last selected type.
	 *
	 * @returns String or undefined.
	 */
	// eslint-disable-next-line no-restricted-syntax
	public get lastSelectedType(): string {
		return this._type;
	}

	/**
	 * Get the C2OA list.
	 *
	 * @returns The c20a list.
	 */
	public getC2oaDataValue(): C2oa[] {
		return this.send.c20aData.getValue();
	}

	/**
	 * Save C2OA.
	 *
	 * @param model C2oa.
	 * @returns Observable<C2oa>.
	 */
	public save(model: C2oa): Observable<C2oa> {
		return this._wrapLoader<C2oa>(this.post(this._httpServiceObjectType(this.Actions.save, model)))
			.pipe(map(this._map.bind(this)));
	}

	/**
	 * Get entity by id.
	 *
	 * @param id String.
	 * @returns Observable<C2oa>.
	 */
	public getById(id: string): Observable<C2oa> {
		return this._wrapLoader<C2oa>(this.post(this._httpServiceObjectType(this.Actions.getById, id)))
			.pipe(map(this._map.bind(this)));
	}

	/**
	 * Get entity by name.
	 *
	 * @param name String.
	 * @returns Observable<C2oa>.
	 */
	public getByName(name: string): Observable<C2oa> {
		return this._wrapLoader<C2oa>(this.post(this._httpServiceObjectType(this.Actions.getByName, name)))
			.pipe(map(this._map.bind(this)));
	}

	/**
	 * Delete entity by id.
	 *
	 * @param id String.
	 * @returns Observable<string>.
	 */
	public remove(id: string): Observable<string> {
		return this._wrapLoader<string>(this.delete<string>(this._httpServiceObjectType(this.Actions.delete, id)));
	}

	/**
	 * Change status of the C2OA.
	 *
	 * @param model C2oa.
	 * @param status ApprovalStatusEnum's value.
	 * @returns Observable<C2oa>.
	 */
	public changeStatus(model: C2oa, status: ApprovalStatusEnum): Observable<C2oa> {
		//
		let action = this.Actions.reject;
		switch (status) {
			case ApprovalStatusEnum.Approved:
				action = this.Actions.approve;
				break;
			case ApprovalStatusEnum.Submitted:
				action = this.Actions.submit;
				break;
			default:
				break;
		}

		return this._wrapLoader<C2oa>(this.post(this._httpServiceObjectType(action, model)))
			.pipe(map(this._map.bind(this)));
	}

	/**
	 * Request to refresh the data.
	 *
	 * @param type String.
	 * @returns Observable<C2oa[]>.
	 */
	public getC2oaList(type: string): Observable<C2oa[]> {
		this._type = type;
		let request: Observable<C2oa[]> = new Observable<C2oa[]>();

		if (type === 'All') {
			request = this._getList();
		} else {
			request = this._getListByType(type);
		}

		return this._wrapLoader<C2oa[]>(request).pipe(map(this._mapList.bind(this)));
	}

	/**
	 * Check if we can change the c2oa selection model.
	 *
	 * @returns Observable<boolean>.
	 */
	public canChangeSelection(): Observable<boolean> {
		return new Observable((observer: Observer<boolean>) => {
			this.ask('isDetailsChanged', (isChanged) => {
				if (!isChanged) {
					observer.next(true);
					observer.complete();
				} else {
					this.send.changeSelectionWarning.next(true);

					this.canChangeSelectionSub?.unsubscribe();
					this.canChangeSelectionSub =
						this.listen.canChangeSelection
							.subscribe((yes: boolean) => {
								observer.next(yes);
								observer.complete();
							});
				}
			});
		});
	}

	// ------

	/**
	 * Ask a question.
	 *
	 * @param event C2oaQuestionEvent.
	 * @param callback (data: any) => void.
	 */
	public ask(event: C2oaQuestionEvent, callback: (data: unknown) => void): void {
		const answerFunc = this._answer[event] && typeof this._answer[event] === 'function';
		if (answerFunc) {
			// eslint-disable-next-line @typescript-eslint/ban-types
			(this._answer[event] as Function)(callback);
		} else {
			callback(null);
		}
	}

	/**
	 * Register who's going to answer to a specific question.
	 *
	 * @param event C2oaQuestionEvent.
	 * @param answerFunc (callback: (data: any) => void) => void.
	 */
	public registerAnswer(event: C2oaQuestionEvent, answerFunc: (callback: (data: unknown) => void) => void): void {
		if (this._answer[event]) {
			// console.log('An answer function has already been registered for: ', event);
		}
		this._answer[event] = answerFunc;

		// console.log('Answered callback registered', this._answer[event]);
	}

	// Private methods ------

	/**
	 * Get all entities, no filter.
	 *
	 * @returns Observable<C2oa[]>.
	 * @private
	 */
	private _getList(): Observable<C2oa[]> {
		return this.get<C2oa[]>(this._httpServiceObjectType(this.Actions.getList));
	}

	/**
	 * Get entities filtered by type.
	 *
	 * @param type String.
	 * @returns Observable<C2oa[]>.
	 * @private
	 */
	private _getListByType(type: string): Observable<C2oa[]> {
		return this.post<C2oa[]>(this._httpServiceObjectType(this.Actions.getListByType, type));
	}

	/**
	 * Wrap all requests in order to show/hide the loader.
	 *
	 * @param request Observable<T>.
	 * @returns Observable<T>.
	 * @private
	 */
	private _wrapLoader<T>(request: Observable<T>): Observable<T> {
		this._store.dispatch(showLoader());

		return request
			.pipe(tap({
				next: () => this._store.dispatch(hideLoader()),
				error: () => this._store.dispatch(hideLoader())
			}));
	}

	/**
	 * Map extra properties to C2oa models that we'll use in the component.
	 *
	 * @param items C2oa as they are from the server.
	 * @returns C2oa list mapped.
	 * @private
	 */
	private _mapList(items: C2oa[]): C2oa[] {
		items.forEach(this._map.bind(this));
		return items;
	}

	/**
	 * Map extra properties to C2oa model that we'll use in the component.
	 *
	 * @param item C2oa as it is from the server.
	 * @returns C2oa mapped.
	 * @private
	 */
	private _map(item: C2oa): C2oa {
		const agsLocation = item.location.areaAgsLocation;
		const shapeType = item.location.areaAgsLocation?.substring(1, agsLocation?.indexOf(':'));

		let shape: string | null = null;

		switch (shapeType) {
			case ('AGSLine'):
				shape = 'Polyline';
				break;
			default:
				shape = shapeType ? shapeType.replace('AGS', '') : '';
				break;
		}

		item.shapeSvgUrl = C2oaShapesHelper.convertShapeTypeToImage(shape);
		item.shape = shape;

		item.approvalStatusSvgFill = '#dddddd';

		switch (item.approvalStatus) {
			case ApprovalStatusEnum.Draft:
				item.approvalStatusSvgUrl = 'assets/visuals/edit.svg';
				break;
			case ApprovalStatusEnum.Approved:
				item.approvalStatusSvgUrl = 'assets/visuals/Images/approve_16.png';
				item.approvalStatusSvgFill = '#6CC316';
				break;
			case ApprovalStatusEnum.Submitted:
				item.approvalStatusSvgUrl = 'assets/visuals/Images/C2OAStatus/submit_16.png';
				break;
			default:
		}
		item.isNew = false;
		item.isDisabled = false;
		return item;
	}

	/**
	 * Default HttpServiceObjectType for this service.
	 *
	 * @param actionName String.
	 * @param body Any.
	 * @param useCache Boolean.
	 * @returns HttpServiceObjectType.
	 * @private
	 */
	private _httpServiceObjectType(actionName: string, body?: unknown, useCache?: boolean): HttpServiceObjectType {
		return {
			serviceEndPoint: ApiEndpointConstants.C2oaService,
			actionName,
			body,
			useCache
		};
	}
}
