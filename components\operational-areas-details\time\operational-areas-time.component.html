<div class="flex formArea">
	<vs-groupbox [label]="'translations.operational.areas.Details.Time.ActivityStatus'|translate">
		<kendo-gridlayout [rows]="[40, 300]" [cols]="[250, 'auto']" [gap]="15">
			<kendo-gridlayout-item [col]="1" [row]="1" [colSpan]="2">
				<vs-radiobutton [isLight]='false' [buttons]="statusRadioBtns" [readonly]="isDisabled || isReadonly || model.isDisabled"
					(itemClickEvent)="statusClick($any($event))">
				</vs-radiobutton>
			</kendo-gridlayout-item>
			<kendo-gridlayout-item [col]="1" [row]="2">
				<vs-groupbox [label]="'translations.operational.areas.Details.Time.TimePeriod'|translate">
					<div class="flex flex-col p-2">
						<vs-datetimepicker name="From"
							[label]="'translations.operational.areas.Details.Time.From'|translate"
							[title]="'translations.operational.areas.Details.Tooltips.InputFrom'|translate"
							[(ngModel)]="dates.from" [disabled]="!isActiveOnPeriod || isReadonly || model.isDisabled"></vs-datetimepicker>
						<div class="mt-2 mb-2">
							<vs-radiobutton id="fromToRadioBtns" [buttons]="fromToRadioBtns"
								[readonly]="!isActiveOnPeriod || isReadonly"
								(itemClickEvent)="fromToClick($any($event))">
							</vs-radiobutton>
						</div>
						<vs-datetimepicker name="To"
							[label]="'translations.operational.areas.Details.Time.To'|translate" [(ngModel)]="dates.to"
							[title]="'translations.operational.areas.Details.Tooltips.InputTo'|translate"
							[disabled]="!isActiveOnPeriod || model.isDisabled"  *ngIf="isRangePeriod"> </vs-datetimepicker>
						<div class="mt-4 mb-2 flex flex-row-reverse">
							<vs-button [disabled]="!isActiveOnPeriod || isReadonly || model.isDisabled" (clickEvent)="add()"
								[attr.title]="(!isActiveOnPeriod || isReadonly) ? null : 'translations.operational.areas.Details.Tooltips.AddTime'|translate">
								<div class="flex items-center">
									<svg-icon src="../../assets/visuals/add.svg" [stretch]="true"
										[svgStyle]="{ 'width.px': 16, 'height.px': 16, fill: '#ebebeb' }">
									</svg-icon>
									<label
										class="ml-2 text-white">{{'translations.operational.areas.Details.Time.Add'|translate}}</label>
								</div>
							</vs-button>
						</div>
					</div>
				</vs-groupbox>
			</kendo-gridlayout-item>
			<kendo-gridlayout-item [col]="2" [row]="2">
				<div class="gridContent h-full">
					<vs-matrixview [data]="effectiveTimeData" class="h-full"
						[gridTitle]="('translations.operational.areas.Details.Time.EffectiveTime'|translate) + '*'"
						[selectableMode]="'single'" [showToolbarColumnVisibility]="false" [showToolbar]="false">

						<kendo-grid-column title="#" [width]="20">
							<ng-template kendoGridHeaderTemplate let-dataItem>
								<span [title]="'translations.operational.areas.Details.Tooltips.RowNumber'|translate">
									#
								</span>
							</ng-template>
							<ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
								<span>{{rowIndex + 1}}</span>
							</ng-template>
						</kendo-grid-column>
						<kendo-grid-column field="fromDate" title="From" [width]="75">
							<ng-template kendoGridHeaderTemplate let-dataItem>
								<span [title]="'translations.operational.areas.Details.Tooltips.From'|translate">
									{{'translations.operational.areas.Details.Time.From'|translate}}
								</span>
							</ng-template>
							<ng-template kendoGridCellTemplate let-dataItem>
								<span [innerDate]="dataItem.fromDate"></span>
							</ng-template>
						</kendo-grid-column>
						<kendo-grid-column field="toDate" title="To" [width]="75">
							<ng-template kendoGridHeaderTemplate let-dataItem>
								<span [title]="'translations.operational.areas.Details.Tooltips.To'|translate">
									{{'translations.operational.areas.Details.Time.To'|translate}}
								</span>
							</ng-template>
							<ng-template kendoGridCellTemplate let-dataItem>
								<span [innerDate]="dataItem.toDate"></span>
							</ng-template>
						</kendo-grid-column>
						<kendo-grid-column field="none" title="" [width]="21">
							<ng-template kendoGridCellTemplate let-dataItem>
								<vs-button [isRibbon]="false" [isBig]="false" [disabled]="isReadonly || model.isDisabled"
									[title]="'translations.operational.areas.Details.Time.Delete'|translate"
									(clickEvent)="remove(dataItem)" imageUrl="assets/visuals/drawingRibbon/delete.svg">
								</vs-button>
							</ng-template>
						</kendo-grid-column>
					</vs-matrixview>
				</div>
			</kendo-gridlayout-item>
		</kendo-gridlayout>
	</vs-groupbox>
</div>

