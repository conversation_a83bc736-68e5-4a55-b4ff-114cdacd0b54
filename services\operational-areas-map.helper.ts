/* eslint-disable no-lonely-if */
/* eslint-disable sort-imports */
/* eslint-disable complexity */

import * as LoadedModulesSelector from '../../../shared/store/selectors/loaded-modules.selector';

import { AgsResources, AgsShape, AgsShapeFactory, AirC2IoLayer, AirbaseProperties, C2oaRenderer, Colors, ContextMenuItemClickEventArgs, DashType, GraphicSelectionService, IBaseIo, LayerType, MapViewModel, MilitaryFillSymbol, SolidColorBrush, SymbologyType, Visibility, VsCircularContextMenuItem } from '@airc2is/ags';
import { ApprovalStatus, C2OAActivityType } from '../../query-manager/utils/query-manager-attribute-types';

import { ActivityStatusEnum } from './enum/activity-status.enum';
import { AppState } from 'src/app/shared/store/states/app.state';
import BaseIoEventArgs from '@airc2is/ags/lib/Presentation/Events/Arguments/base-io-event-args';
import BsoLayer from '@airc2is/ags/lib/Presentation/Controls/CDM/bso-layer';
import { C2oa } from 'src/app/types/operational-areas';
import { Injectable } from '@angular/core';
import { LocationObject } from 'src/app/types/location-object.class';
import MapObjectClickEventArgs from '@airc2is/ags/lib/Presentation/Events/Arguments/map-object-click-event-args';
import { ObjectHelper } from '@airc2is/aus';
import SelectionChangedEventArgs from '@airc2is/ags/lib/Presentation/Controls/CDM/selection-changed-event-args';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { WktConverter } from '../../../shared/converters/wkt-converter/wkt-converter';

/**
 *
 */
@Injectable({ providedIn: 'root' })

export class C2oaMapHelper {
	entityLayerSelectionChangedOnMapC20A: Subject<{ sender: BaseIoEventArgs, e: SelectionChangedEventArgs }> = new Subject();
	contextMenuItemClicked: Subject<LocationObject> = new Subject();
	baseIoDoubleClicked: Subject<any> = new Subject();
	entityLayerText = 'Entities';
	entityLayer!: AirC2IoLayer;
	menuItems: VsCircularContextMenuItem[] = [];
	entityChanged: Subject<AirC2IoLayer> = new Subject();
	public activeSubModuleName = '';
	selectionService = GraphicSelectionService.getInstance();

	/**
	 * Constructor.
	 *
	 * @param store Store AppState.
	 */
	constructLayers(store: Store<AppState>): void {
		let menu = new Array<VsCircularContextMenuItem>();
		menu = this._createContextMenu(menu);

		this.entityLayer = new AirC2IoLayer('1', 'C2OA', menu);
		this.entityLayer.layerRenderer = new C2oaRenderer();
		this.entityLayer.canBeDynamicOverlay = true;
		this.entityLayer.label = this.entityLayerText;
		this.entityLayer.canAddToConops = true;
		this.entityLayer.layerType = LayerType.C2OaLayer.toString();


		this.initializeLayerEvents();
		const layers = new Array<BsoLayer>();
		layers.push(this.entityLayer);

		this._initializeLayer('C2OA', layers);

		store.select(LoadedModulesSelector.getActiveSubModule).subscribe(value => {
			this.activeSubModuleName = value ? value.moduleName : '';
		});
	}

	/**
	 * The initialize layer events.
	 */
	initializeLayerEvents(): void {
		this.selectionService.selectionChanged.addListener(this.onEntityLayerSelectionChanged.bind(this));
		this.entityLayer.contextMenuItemClicked.addListener(this.onContextMenuItemClicked.bind(this));
		this.entityLayer.baseIoDoubleClicked.addListener(this.onBaseIoDoubleClicked.bind(this));
		this.entityLayer.locationChanged.addListener(this.onTrackLocationChange.bind(this));
	}

	/**
	 * The dispose layer events.
	 */
	disposeLayerEvents(): void {
		this.selectionService.selectionChanged.removeListener(this.onEntityLayerSelectionChanged);
		this.entityLayer.contextMenuItemClicked.removeListener(this.onContextMenuItemClicked);
		this.entityLayer.baseIoDoubleClicked.removeListener(this.onBaseIoDoubleClicked);
	}

	/**
	 * The dispose.
	 */
	dispose(): void {
		this.disposeLayerEvents();
		MapViewModel.instance().removeLayer(this.entityLayer.getEsriLayer());
		this.entityLayer.clear();
		this.entityLayer.dispose();
		this.entityLayer = {} as AirC2IoLayer;
	}

	/**
	 * The entity layer selection changed.
	 *
	 * @param sender The sender.
	 * @param e Map Object Click Event Arguments.
	 */
	onEntityLayerSelectionChanged(sender: BaseIoEventArgs, e: SelectionChangedEventArgs): void {
		if (this.entityLayerSelectionChangedOnMapC20A) {
			this.entityLayerSelectionChangedOnMapC20A.next({
				sender,
				e
			});
		}
	}

	/**
	 * The on selection changed.
	 *
	 * @param sender The sender.
	 * @param e Map Object Click Event Arguments.
	 */
	onContextMenuItemClicked(sender: any, e: ContextMenuItemClickEventArgs): void {
		this.contextMenuItemClicked.next(e.baseIo as LocationObject);
	}

	/**
	 * The on base ıo double clicked.
	 *
	 * @param o Double clicked IO Object.
	 * @param e Map Object Click Event Arguments.
	 */
	onBaseIoDoubleClicked(o: any, e: MapObjectClickEventArgs): void {
		this.baseIoDoubleClicked.next({
			o,
			e
		});
	}

	/**
	 * The on base ıo double clicked.
	 *
	 * @param e Map Object Click Event Arguments.
	 */
	onTrackLocationChange(e: AirC2IoLayer): any {
		this.entityChanged.next(e);
	}

	/**
	 * Activate PanTool.
	 */
	activatePanTool(): void {
		MapViewModel.instance().activatePanTool();
	}

	/**
	 * Add entities items.
	 *
	 * @param areaObjects The sender.
	 * @returns Void.
	 */
	addEntityItems(areaObjects: C2oa[]): void {
		areaObjects.forEach(item => {
			if (!ObjectHelper.isNullOrEmpty(item.location)) {
				this.addEntityItem(item, false);
			}
		});

		const guidList = areaObjects.map(x => x.id);
		const baseIoList = this.entityLayer.getIos();
		const deletedIos = new Array<IBaseIo>();


		baseIoList.forEach(baseIo => {
			if (baseIo && baseIo.baseIoId && !guidList.includes(baseIo.baseIoId)) {
				baseIo.isMovable = false;
				deletedIos.push(baseIo);
			}
		});

		deletedIos.forEach(deletedIo => {
			this.entityLayer.removeIo(deletedIo);
		});
	}


	/**
	 * Add entity item function.
	 *
	 * @param item LocationObject item.
	 * @param isVisible Boolean.
	 * @param borderBrush SolidColorBrush.
	 * @param fillBrush SolidColorBrush.
	 * @param symbologyType SymbologyType.
	 * @param oldId OldId.
	 */
	public addEntityItem(
		item: C2oa,
		isVisible: boolean | null = null,
		borderBrush: SolidColorBrush | null = {} as SolidColorBrush,
		fillBrush: SolidColorBrush | null = null,
		symbologyType: SymbologyType = SymbologyType.App6A,
		oldId: string | null = null): void {
		if (!item.location) {
			return;
		}

		const foundItem = oldId ? this.entityLayer.getById(oldId) : this.entityLayer.getById(item.id);

		let visibleState = true;

		if (foundItem) {
			visibleState = foundItem.visibility === Visibility.Visible;
			this.entityLayer.removeIo(foundItem);
		}

		item.location.baseIoId = item.id;
		if (ObjectHelper.isNullOrEmpty(item.location.ioGeometry)) {
			if (!ObjectHelper.isNullOrEmpty(item.location.areaAgsLocation)) {
				item.location.labelContent = '';
				item.location.ioGeometry = AgsShapeFactory.fromString(item.location.areaAgsLocation);
			} else {
				const areaLocation = item.location.areaLocation || '';
				item.location.ioGeometry = WktConverter.convertFromWkt(areaLocation);
			}
		}
		if (ObjectHelper.isNullOrEmpty(borderBrush)) {
			const c2oa = item;
			if (c2oa && c2oa.c2oaType) {
				borderBrush = this._getBorderColorByType(c2oa.c2oaType);
			}
		}

		if (ObjectHelper.isNullOrEmpty(fillBrush)) {
			fillBrush = new SolidColorBrush(Colors.transparent);
		}

		item.location = Object.assign(new LocationObject(), item.location);

		item.location.constructorCode();
		item.location.labelContent = this.getLabelContent(item);
		item.location.mapTipContent = this.getMapTipContent(item);
		const c2Oa1 = item;
		let dashTypeByStatus = DashType.Solid;

		if (c2Oa1 && (c2Oa1.approvalStatus === ApprovalStatus.Submitted.StringValue || c2Oa1.approvalStatus === ApprovalStatus.Draft.StringValue)) {
			dashTypeByStatus = DashType.Dot;
		}

		if (c2Oa1 && this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
			dashTypeByStatus = DashType.Solid;
		}

		if (c2Oa1 && !this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
			dashTypeByStatus = DashType.LongDash;
		}

		if (!item.location.symbology) {
			const militaryFillSymbol = new MilitaryFillSymbol();
			if (borderBrush) {
				militaryFillSymbol.borderBrush = borderBrush;
			}
			militaryFillSymbol.fillBrush = fillBrush;
			militaryFillSymbol.borderThickness = 2;
			militaryFillSymbol.opacity = 0.7;
			militaryFillSymbol.lineWidth = 2;
			militaryFillSymbol.dashType = dashTypeByStatus;
			item.location.symbology = militaryFillSymbol;
		} else {
			const fillSymbol = item.location.symbology as MilitaryFillSymbol;
			if (fillSymbol) {
				if (borderBrush) {
					fillSymbol.borderBrush = borderBrush;
				}
				fillSymbol.fillBrush = fillBrush;
				fillSymbol.dashType = dashTypeByStatus;
			}
		}

		item.location.isMovable = this.getIsMoveableStatus(item); // TODO: Check HasUpdatePermission
		console.log('isVisible', isVisible);

		item.location.visibility = isVisible === true ? Visibility.Visible : Visibility.Collapsed;
		// item.location.visibility = Visibility.Collapsed;

		console.log('item', item.location);

		this.entityLayer.addIo(item.location);
	}

	/**
	 * Remove all entity items from layer.
	 *
	 * @returns Void.
	 */
	removeAllEntityItemsFromLayer(): void {
		if (this.entityLayer !== null) {
			this.entityLayer.clear();
		}
	}


	/**
	 * Remove entity item from layer.
	 *
	 * @param item LocationObject.
	 * @returns Void.
	 */
	removeEntityItem(item: C2oa): void {
		if (item === null || item.location === null) {
			return;
		}
		item.location.baseIoId = item.id;
		const foundItem = this.entityLayer.getById(item.location.baseIoId);
		if (foundItem !== null) {
			this.entityLayer.removeIo(foundItem);
		}
	}

	/**
	 * Remove entity items from layer.
	 *
	 * @param items LocationObject array.
	 * @returns Void.
	 */
	removeEntityItems(items: C2oa[]): void {
		items.forEach((item) => {
			if (item.location) {
				this.removeEntityItem(item);
			}
		});
	}

	/**
	 * Remove all layers.
	 *
	 * @returns Void.
	 */
	removeLayers(): void {
		MapViewModel.instance().removeLayer(this.entityLayer.getEsriLayer());
	}

	/**
	 * Undo selected object.
	 *
	 * @param obj IBaseIo.
	 * @returns Void.
	 */
	undoIo(obj: IBaseIo): void {
		this.entityLayer.undoIoLocation(obj);
	}

	/**
	 * Selects the object.
	 *
	 * @param obj The object.
	 * @returns Void.
	 */
	selectObject(obj: any): void {
		MapViewModel.instance().selectMapObject(obj);
	}


	/**
	 * Gets the map object.
	 *
	 * @param ioId The io identifier.
	 * @returns Base Io object or null.
	 */
	getMapObject(ioId: string): IBaseIo | null {
		const foundItem = this.entityLayer.getById(ioId);
		if (!ObjectHelper.isNullOrEmpty(foundItem)) {
			return foundItem;
		}
		return null;
	}


	/**
	 * Updates the entity item.
	 *
	 * @param item Location Object.
	 * @returns Void.
	 */
	updateEntityItem(item: C2oa): C2oa | null {
		if (ObjectHelper.isNullOrEmpty(item.location)) {
			return null;
		}
		let borderBrush: SolidColorBrush | null = null;
		let fillBrush: SolidColorBrush | null = null;

		item.location.baseIoId = item.id;

		if (!ObjectHelper.isNullOrEmpty(item.location.areaAgsLocation)) {
			item.location.ioGeometry = AgsShapeFactory.fromString(item.location.areaAgsLocation);
		} else {
			const areaLocation = item.location.areaLocation || '';
			item.location.ioGeometry = WktConverter.convertFromWkt(areaLocation);
		}

		const c2oa = item;
		if (c2oa && c2oa.c2oaType) {
			borderBrush = this._getBorderColorByType(c2oa.c2oaType);
		}

		if (ObjectHelper.isNullOrEmpty(fillBrush)) {
			fillBrush = new SolidColorBrush(Colors.transparent);
		}

		item.location = Object.assign(new LocationObject(), item.location);

		item.location.constructorCode();
		item.location.labelContent = this.getLabelContent(item);
		item.location.mapTipContent = this.getMapTipContent(item);
		const c2Oa1 = item;
		let dashTypeByStatus = DashType.Solid;

		if (c2Oa1 && (c2Oa1.approvalStatus === ApprovalStatus.Submitted.StringValue || c2Oa1.approvalStatus === ApprovalStatus.Draft.StringValue)) {
			dashTypeByStatus = DashType.Dot;
		}

		if (c2Oa1 && this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
			dashTypeByStatus = DashType.Solid;
		}

		if (c2Oa1 && !this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
			dashTypeByStatus = DashType.LongDash;
		}

		const militaryFillSymbol = new MilitaryFillSymbol();
		if (borderBrush) {
			militaryFillSymbol.borderBrush = borderBrush;
		}
		militaryFillSymbol.fillBrush = fillBrush;
		militaryFillSymbol.borderThickness = 2;
		militaryFillSymbol.opacity = 0.7;
		militaryFillSymbol.lineWidth = 2;
		militaryFillSymbol.dashType = dashTypeByStatus;
		item.location.symbology = militaryFillSymbol;
		item.location.visibility = Visibility.Visible;
		item.location.isMovable = this.getIsMoveableStatus(item);
		return item;
	}

	/**
	 * Updates the entity location.
	 *
	 * @param item Location Object.
	 * @returns Void.
	 */
	updateEntityLocation(item: C2oa): void {
		if (ObjectHelper.isNullOrEmpty(item.location)) {
			return;
		}
		let borderBrush: SolidColorBrush | null = null;
		let fillBrush: SolidColorBrush | null = null;

		const foundItem = this.entityLayer.getById(item.id);
		if (foundItem) {
			if (item.location.areaAgsLocation) {
				const foundItemLocation = foundItem as LocationObject;
				foundItemLocation.areaAgsLocation = item.location.areaAgsLocation;
				foundItem.ioGeometry = AgsShapeFactory.fromString(item.location.areaAgsLocation);
			} else {
				if (item.location.areaLocation) {
					foundItem.ioGeometry =  WktConverter.convertFromWkt(item.location.areaLocation);
				}
			}

			const c2oa = item;

			if (c2oa !== null) {
				borderBrush = this._getBorderColorByType(c2oa.c2oaType);
			}
			fillBrush = new SolidColorBrush(Colors.transparent);

			item.location = Object.assign(new LocationObject(), item.location);

			item.location.constructorCode();
			item.location.labelContent = this.getLabelContent(item);
			item.location.mapTipContent = this.getMapTipContent(item);
			const c2Oa1 = item;
			let dashTypeByStatus = DashType.Solid;

			if (c2Oa1 && (c2Oa1.approvalStatus === ApprovalStatus.Submitted.StringValue || c2Oa1.approvalStatus === ApprovalStatus.Draft.StringValue)) {
				dashTypeByStatus = DashType.Dot;
			}

			if (c2Oa1 && this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
				dashTypeByStatus = DashType.Solid;
			}

			if (c2Oa1 && !this._isC2oaActive(c2Oa1) && c2Oa1.approvalStatus === ApprovalStatus.Approved.StringValue) {
				dashTypeByStatus = DashType.LongDash;
			}

			if (!ObjectHelper.isNullOrEmpty(c2Oa1) && !ObjectHelper.isNullOrEmpty(c2Oa1.createdBy)) {
				const militaryFillSymbol = new MilitaryFillSymbol();
				if (borderBrush) {
					militaryFillSymbol.borderBrush = borderBrush;
				}
				militaryFillSymbol.fillBrush = fillBrush;
				militaryFillSymbol.borderThickness = 2;
				militaryFillSymbol.opacity = 0.7;
				militaryFillSymbol.lineWidth = 2;
				militaryFillSymbol.dashType = dashTypeByStatus;
				foundItem.symbology = militaryFillSymbol;
			}

			if (!ObjectHelper.isNullOrEmpty(foundItem.symbology)) {
				const militaryFillSymbol = new MilitaryFillSymbol();
				if (borderBrush) {
					militaryFillSymbol.borderBrush = borderBrush;
				}
				militaryFillSymbol.fillBrush = fillBrush;
				militaryFillSymbol.borderThickness = 2;
				militaryFillSymbol.opacity = 0.7;
				militaryFillSymbol.lineWidth = 2;
				militaryFillSymbol.dashType = dashTypeByStatus;
				foundItem.symbology = militaryFillSymbol;
			} else {
				const fillsymbol = new MilitaryFillSymbol(foundItem.symbology);
				if (fillsymbol !== null) {
					fillsymbol.dashType = dashTypeByStatus;
					if (borderBrush !== null) {
						fillsymbol.borderBrush.color = borderBrush.color;
					}
				}
			}
			foundItem.isMovable = this.getIsMoveableStatus(item); // && this.HasUpdatePermission;
			item.location = foundItem as LocationObject;
			if (item.location !== null) {
				item.location.isMovable = this.getIsMoveableStatus(item);
			}
		}
	}

	/**
	 * Get all locations.
	 *
	 * @returns A list with all the locations.
	 */
	getLocations(): IBaseIo[] {
		return this.entityLayer.getIos();
	}


	/**
	 * Get io geometry.
	 *
	 * @param model C2oa Model.
	 * @returns An AgsShape(ioGeometry).
	 */
	getIoGeometry(model: C2oa): AgsShape | undefined {
		const locations = this.entityLayer.getIos();
		const location = locations.find((i) => i.baseIoId === model.id);
		return location?.ioGeometry;
	}

	/**
	 * Redo selected object.
	 *
	 * @param baseIo IO Object..
	 * @returns Void.
	 */
	redoIo(baseIo: IBaseIo): void {
		this.entityLayer.redoIoLocation(baseIo);
	}


	/**
	 * Zooms to entity.
	 *
	 *@returns Void.
	 */
	zoomToEntityLayer(): void {
		MapViewModel.instance().zoomToExtent(this.entityLayer.getEsriLayer().fullExtent);
	}


	/**
	 * Zooms to entity.
	 *
	 * @param item The item.
	 * @param isVisible IsVisible.
	 */
	public zoomToEntity(item: C2oa, isVisible: boolean): void {
		if (!item || !item.location) {
			return;
		}
		item.location.visibility = isVisible ? Visibility.Visible : Visibility.Collapsed;
		MapViewModel.instance().zoomTo(item.location);
	}

	/**
	 * Get MapTip Content.
	 *
	 * @param item LocationObject.
	 * @returns Void.
	 */
	getMapTipContent(item: C2oa): string {
		let mapTip = '';
		let nvgText = '';
		const c2oaObject = item;

		if (c2oaObject !== null) {
			let timePeriodText = '';
			let nvgTimePeriodText = '';
			if (c2oaObject.timePeriods !== null) {
				let i = 1;
				for (const timePeriod of c2oaObject.timePeriods) {
					timePeriodText += `\n${timePeriod.fromDate} - ${timePeriod.toDate}`;
					nvgTimePeriodText += `TimePeriod${i}:${timePeriod.fromDate.toLocaleString('en-US', {
						year: 'numeric',
						month: 'short',
						day: 'numeric',
						hour: '2-digit',
						minute: '2-digit',
						timeZoneName: 'short'
					})} - ${timePeriod.toDate.toLocaleString('en-US', {
						year: 'numeric',
						month: 'short',
						day: 'numeric',
						hour: '2-digit',
						minute: '2-digit',
						timeZoneName: 'short'
					})}\n`;
					i++;
				}
			}

			const altitudeText = `${c2oaObject.altitudeMin || ''} - ${c2oaObject.altitudeMax || ''}`;
			mapTip = `Name: ${c2oaObject.c2oaName} \nType: ${c2oaObject.c2oaType} \nDescription : ${c2oaObject.description || ''} \nAltitude: ${altitudeText} \nStatus: ${this._getActivityStatusToolTip(c2oaObject)} \n ${c2oaObject.isSimulated ? 'Simulated' : 'Not Simulated'} \nApproval Status: ${c2oaObject.approvalStatus} \nTime Periods: ${timePeriodText}`;
			nvgText = `Name:${c2oaObject.c2oaName}\nType:${c2oaObject.c2oaType}\nDescription:${c2oaObject.description || ''}\nAltitude:${altitudeText}\nStatus:${this._getActivityStatusToolTip(c2oaObject)}\nSimulation Status:${c2oaObject.isSimulated ? 'Simulated' : 'Not Simulated'}\nApproval Status:${c2oaObject.approvalStatus}\n${nvgTimePeriodText}`;

			if (!item.location.airbaseProperties) {
				item.location.airbaseProperties = new AirbaseProperties();
			}
			item.location.airbaseProperties.field1 = 'C2OA';
			if (nvgText.endsWith('\n')) {
				nvgText = nvgText.slice(0, -1);
			}
			item.location.airbaseProperties.field2 = nvgText;
		}

		return mapTip;
	}

	/**
	 * Get label content.
	 *
	 *@param item LocationObject.
	 *@returns String.
	 */
	getLabelContent(item: C2oa): string {
		const c2oaObject = item;
		let label = '';
		if (c2oaObject !== null) {
			label = c2oaObject.c2oaName;
		}

		return label;
	}

	/**
	 * Get is moveable status.
	 *
	 *@param item LocationObject.
	 *@returns Boolean.
	 */
	getIsMoveableStatus(item: C2oa): boolean {
		if (item === null) {
			// throw new ArgumentNullException("item");
			return false; // TODO; Check is return false is OK
		}
		const c2OaObject = item;
		if (c2OaObject !== null && c2OaObject.approvalStatus === ApprovalStatus.Draft.StringValue) {
			return true;
		}

		return false;
	}

	/**
	 * The initialize layer.
	 *
	 * @param groupName The group name.
	 * @param layers The layers.
	 */
	private _initializeLayer(groupName: string, layers: Array<BsoLayer>): void {
		MapViewModel.instance().addLayer4(groupName, layers);
	}


	/**
	 * Creates the context menu.
	 *
	 * @param menu Menu.
	 * @returns Array menu.
	 */
	private _createContextMenu(menu: Array<VsCircularContextMenuItem>): Array<VsCircularContextMenuItem> {
		const item1 = new VsCircularContextMenuItem();
		item1.icon = AgsResources.defenceDesignShowDetails;
		item1.title = 'Show details of entity';
		item1.tag = { id: '2' };

		menu = [item1];
		return menu;
	}

	/**
	 * Creates the context menu.
	 *
	 * @param c2oa C2oaObject.
	 * @returns Boolean.
	 */
	private _isC2oaActive(c2oa: C2oa): boolean {
		let retVal = false;
		if (c2oa === null || ObjectHelper.isNullOrEmpty(c2oa.activityStatus)) {
			return retVal;
		}

		switch (c2oa.activityStatus) {
			case ActivityStatusEnum.ActiveAlways:
				retVal = true;
				break;
			case ActivityStatusEnum.ActiveOnPeriod:
			case ActivityStatusEnum.Dormant:
				retVal = false;
				break;
			default:
				break;
		}

		return retVal;
	}


	/**
	 * Get activity Status tooltip.
	 *
	 * @param c2oa C2oaObject.
	 * @returns String.
	 */
	private _getActivityStatusToolTip(c2oa: C2oa): string {
		let retVal = '';

		switch (c2oa.activityStatus) {
			case C2OAActivityType.ActiveAlways.StringValue:
				// retVal = ViewResources.DetailTimePeriodActivityStatusActiveAlways;
				retVal = 'Active Always'; // The value for ViewResources.DetailTimePeriodActivityStatusActiveAlways is 'Active always';
				break;
			case C2OAActivityType.Dormant.StringValue:
				// retVal = ViewResources.DetailTimeperiodActivityStatusDormant;
				retVal = 'Dormant'; // The value for ViewResources.DetailTimeperiodActivityStatusDormant is 'Dormant';

				break;
			case C2OAActivityType.ActiveOnPeriod.StringValue:
				if (c2oa.timePeriods) {
					for (const periodObject of c2oa.timePeriods) {
						if (new Date() >= periodObject.fromDate && new Date() <= periodObject.toDate) {
							let statusText = '';

							if (periodObject.toDate === new Date(8640000000000000)) {
								statusText = 'Active Always';
							} else {
								statusText = `Active until ${periodObject.toDate}`;
							}

							return statusText;
						}
					}
				}
				// retVal = CommonResources.ConditionTypesWaiting;
				retVal = 'WAITING'; // The value for ViewResources.ConditionTypesWaiting is 'WAITING';
				break;
			default:
		}
		return retVal;
	}

	/**
	 * Get activity Status tooltip.
	 *
	 * @param c2oaType C2oa Type.
	 * @returns SolidColorBrush or null.
	 */
	private _getBorderColorByType(c2oaType: string): SolidColorBrush | null {
		let borderBrush = null;
		switch (c2oaType) {
			case 'JOA':
			case 'KB':
			case 'MEZ':
				borderBrush = new SolidColorBrush(Colors.red);
				break;
			case 'AOR':
			case 'JFSCL':
			case 'FSCM':
			case 'FSSL':
			case 'FLOT':
				borderBrush = new SolidColorBrush(Colors.green);
				break;
			case 'PIM':
				borderBrush = new SolidColorBrush(Colors.black);
				break;
			case 'DAofTBMD':
			case 'TOA':
			case 'TTA':
				borderBrush = new SolidColorBrush(Colors.blue);
				break;
			case 'ROZ':
				borderBrush = new SolidColorBrush(Colors.purple);
				break;
			case 'Generic':
				borderBrush = new SolidColorBrush(Colors.black);
				break;
			default:
				break;
		}
		return borderBrush;
	}
}
