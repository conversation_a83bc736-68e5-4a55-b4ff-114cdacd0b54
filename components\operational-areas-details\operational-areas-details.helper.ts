/* eslint-disable curly */
/* eslint-disable func-style */

import { AasUiPrincipal, AuthorizationOperatorEnum, ObjectHelper } from '@airc2is/aus';

/**
 * Generate field model.
 *
 * @param name String.
 * @param required Boolean.
 * @returns Field model.
 */
function setField(name: string, required = false): { name: string; required: boolean; error: boolean; showError: boolean; } {
	return {
		name,
		required,
		error: false,
		showError: false
	};
}

/**
 * Checks the claims.
 *
 * @param functions Function.
 * @param permissions Permission.
 * @returns Boolean type..
 */
function checkSecurityDemand(functions: string, permissions: string): boolean {
	const currentPrincipal = AasUiPrincipal.getInstance();
	const demand = {
		items: [{
			function: functions,
			permission: permissions
		}],
		operator: AuthorizationOperatorEnum.And
	};
	if (ObjectHelper.isNullOrEmpty(demand)) {
		return false;
	}
	if (currentPrincipal.hasRequiredClaims(null, demand)) {
		return true;
	}
	return false;
}

// ----
const isDate = (d: unknown): boolean => d instanceof Date;
const isEmpty = (o: any): boolean => Object.keys(o).length === 0;
const isObject = (o: unknown): boolean => o !== null && typeof o === 'object';
const hasOwnProperty = (o: unknown, ...args: unknown[]): boolean => (Object.prototype.hasOwnProperty.call as any)(o, ...(args as []));
const isEmptyObject = (o: unknown): boolean => isObject(o) && isEmpty(o);
const makeObjectWithoutPrototype = (): unknown => Object.create(null);

const addedDiff = (lhs: any, rhs: any): any => {
	if (lhs === rhs || !isObject(lhs) || !isObject(rhs)) return {};

	return Object.keys(rhs).reduce((acc: any, key) => {
		if (hasOwnProperty(lhs, key)) {
			const difference = addedDiff(lhs[key], rhs[key]);

			if (isObject(difference) && isEmpty(difference)) return acc;

			acc[key] = difference;
			return acc;
		}

		acc[key] = rhs[key];
		return acc;
	}, makeObjectWithoutPrototype());
};

const deletedDiff = (lhs: any, rhs: any): any => {
	if (lhs === rhs || !isObject(lhs) || !isObject(rhs)) return {};

	return Object.keys(lhs).reduce((acc: any, key) => {
		if (hasOwnProperty(rhs, key)) {
			const difference = deletedDiff(lhs[key], rhs[key]);

			if (isObject(difference) && isEmpty(difference)) return acc;

			acc[key] = difference;
			return acc;
		}

		// eslint-disable-next-line no-undefined
		acc[key] = undefined;
		return acc;
	}, makeObjectWithoutPrototype());
};

const updatedDiff = (lhs: any, rhs: any): any => {
	if (lhs === rhs) return {};

	if (!isObject(lhs) || !isObject(rhs)) return rhs;

	if (isDate(lhs) || isDate(rhs)) {
		// eslint-disable-next-line eqeqeq
		if (lhs.valueOf() == rhs.valueOf()) return {};
		return rhs;
	}

	return Object.keys(rhs).reduce((acc: any, key) => {
		if (hasOwnProperty(lhs, key)) {
			const difference = updatedDiff(lhs[key], rhs[key]);

			// If the difference is empty, and the lhs is an empty object or the rhs is not an empty object
			if (isEmptyObject(difference) && !isDate(difference) && (isEmptyObject(lhs[key]) || !isEmptyObject(rhs[key]))) return acc; // return no diff

			acc[key] = difference;
			return acc;
		}

		return acc;
	}, makeObjectWithoutPrototype());
};

// Will delete once we finish development
// This helps at the moment with debug
const detailedDiff = (lhs: any, rhs: any): any => ({
	added: addedDiff(lhs, rhs),
	deleted: deletedDiff(lhs, rhs),
	updated: updatedDiff(lhs, rhs)
});

export { setField, detailedDiff, checkSecurityDemand };
