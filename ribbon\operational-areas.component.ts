import { Airc2isFunctions, Permissions } from '@airc2is/aus';
import { Component, OnInit } from '@angular/core';
import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { C2oa } from 'src/app/types/operational-areas';
import { C2oaService } from '../services/operational-areas.service';
import { MessageUtility } from '@airc2is/vscontrols';
import { TranslateService } from '@ngx-translate/core';

import { checkSecurityDemand } from '../components/operational-areas-details/operational-areas-details.helper';

/**
 * Sample Ribbon Component.
 */
@Component({
	selector: 'app-c2oa-ribbon',
	templateUrl: './operational-areas.component.html',
	styleUrls: ['./operational-areas.component.css']
})
export class C2oaRibbonComponent implements OnInit {
	public c2oaEdit?: C2oa | null;
	public c2oaInEdit = false;
	public isStatusChangeDisabled = false;
	public permissions = {
		canDelete: false,
		canSave: false,
		canSubmit: false,
		canApprove: false,
		canCreate: false
	};

	public approvalStatuses = ApprovalStatusEnum;
	public approvalStatus = {
		isDraft: true,
		isSubmitted: true,
		isApproved: true
	};

	constructor(
		private _c2oaService: C2oaService,
		private _translate: TranslateService) {
		this.permissions.canApprove = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Approve);
		this.permissions.canSubmit = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Submit);
		this.permissions.canDelete = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Delete);
		this.permissions.canCreate = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Create);
		this.permissions.canSave = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Update);
	}

	/**
	 * Ng on init.
	 */
	ngOnInit(): void {
		this._c2oaService.listen.edit
			.subscribe((c2oaEdit) => {
				const c2oa = <C2oa>c2oaEdit;
				this.c2oaEdit = c2oa;
				this.c2oaInEdit = Boolean(c2oa?.id);

				this.approvalStatus.isDraft = c2oa?.approvalStatus === ApprovalStatusEnum.Draft;
				this.approvalStatus.isSubmitted = c2oa?.approvalStatus === ApprovalStatusEnum.Submitted;
				this.approvalStatus.isApproved = c2oa?.approvalStatus === ApprovalStatusEnum.Approved;

				if (c2oa?.c2oaType && c2oa?.id) {
					const type = this._c2oaService.Types.find(type => (<string>c2oa.c2oaType) === type.shortName);
					this.isStatusChangeDisabled = type?.disabledStatusChange ?? false;
				} else {
					this.isStatusChangeDisabled = true;
				}
			});
	}

	/**
	 * Send refresh event.
	 */
	refresh(): void {
		this._c2oaService.send.refresh.next(this._c2oaService.lastSelectedType);
	}

	/**
	 * Send save event.
	 */
	save(): void {
		const canSave = this.permissions.canSave;
		if (!canSave) {
			MessageUtility.showAuthorizationAlert();
			return;
		}
		this._c2oaService.send.save.next(true);
	}

	/**
	 * Send new event.
	 */
	new(): void {
		const canCreate = this.permissions.canCreate;
		if (!canCreate) {
			MessageUtility.showAuthorizationAlert();
			return;
		}
		this._c2oaService.send.create.next(false);
	}

	/**
	 * Send status event.
	 *
	 * @param name Value of ApprovalStatusEnum.
	 */
	status(name: ApprovalStatusEnum): void {
		const canSubmit = this.permissions.canSubmit;
		const canApprove = this.permissions.canApprove;
		if (!canSubmit || !canApprove) {
			MessageUtility.showAuthorizationAlert();
			return;
		}
		this._c2oaService.send.status.next(name);
	}

	/**
	 * Send open history event.
	 */
	history(): void {
		this._c2oaService.send.history.next(true);
	}

	/**
	 * Send duplicate event.
	 */
	duplicate(): void {
		const canCreate = this.permissions.canCreate;
		if (!canCreate) {
			MessageUtility.showAuthorizationAlert();
			return;
		}
		this._c2oaService.send.create.next(true);
	}

	/**
	 * Open delete C2oa.
	 *
	 */
	remove(): void {
		const canDelete = this.permissions.canDelete;
		if (!canDelete) {
			MessageUtility.showAuthorizationAlert();
			return;
		}
		const continueText = this._translate.instant('translations.shared.Continue');
		MessageUtility.showConfirmation({
			title: this._translate.instant('translations.shared.Delete_Confirmation_Title'),
			content: this._translate.instant('translations.operational.areas.Ribbon.DeleteWarning'),
			submitText: continueText
		}).subscribe((action: boolean) => {
			if (action) {
				this._c2oaService.send.delete.next(this.c2oaEdit?.id);
			}
		});
	}
}
