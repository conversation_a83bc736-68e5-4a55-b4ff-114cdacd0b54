import { Observable } from 'rxjs';
/* eslint-disable no-unneeded-ternary */
/* eslint-disable sort-imports */
/* eslint-disable jsdoc/require-returns */
import { ActivityStatusEnum } from 'src/app/modules/operational-areas/services/enum/activity-status.enum';
import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { Guid } from 'guid-typescript';
import { MissionConfidentialityLabelProvider } from '@airc2is/aus';
import { LocationObject } from 'src/app/types/location-object.class';
import { C2oa } from 'src/app/types/operational-areas';
import { C2OTypesType } from '../services/enum/c20a-types.enum';
import { MessageUtility } from '@airc2is/vscontrols';
import { TranslateService } from '@ngx-translate/core';
import { Injectable } from '@angular/core';
import { C2oaService } from '../services/operational-areas.service'; // Your service

/**
 *
 */
@Injectable({ providedIn: 'root' })
export class C2oaConstantsHelper {
	constructor(
    private _translate: TranslateService,
    private _c2oaService: C2oaService
	) {}

	/**
   * Create a new empty c2oa model.
   *
   * @param isDisabled Initial means are the first time loaded.
   * @returns C2oa model.
   */
	newC2oaModel(isDisabled?: boolean): Partial<C2oa> {
		const missionConfidentialityLabelObject = MissionConfidentialityLabelProvider.getInstance().missionConfidentialityLabelObject;
		return <Partial<C2oa>>{
			id: Guid.create().toString(),
			c2oaName: '',
			description: '',
			isNew: true,
			isDisabled: isDisabled ? true : false,
			altitudeMax: '',
			altitudeMin: '',
			activityStatus: ActivityStatusEnum.ActiveAlways,
			location: <LocationObject>{},
			c2oaType: C2OTypesType.Generic,
			approvalStatus: ApprovalStatusEnum.Draft,
			timePeriods: [],
			isPlanned: false,
			isSimulated: false,
			affiliation: null,
			objectType: null,
			confidentialityLabelData: '',
			confidentialityLabelObject: {
				isInformationObject: true,
				classification: missionConfidentialityLabelObject.classification,
				categories: missionConfidentialityLabelObject.categories
			}
		};
	}

	/**
   * Create a duplicate c2oa model.
   *
   * @param model C2oa model.
   * @param c20aList C2oa model.
   * @returns C2oa model.
   */
	duplicateModel(model: C2oa, c20aList: C2oa[]): C2oa {
		const copy = { ...model };
		const override = {
			$id: '',
			isDuplicate: true,
			activityStatus: ActivityStatusEnum.ActiveAlways,
			approvalStatus: ApprovalStatusEnum.Draft,
			approvalComment: '',
			c2oaName: this.duplicateName(model, c20aList),
			timePeriods: []
		};

		return {
			...copy,
			...override
		} as C2oa;
	}

	/**
   * Compare the item without id and isDisabled.
   * @param obj C2oa item.
   * @returns C20a.
   */
	omitId(obj: C2oa): Partial<C2oa> {
		const { id, isDisabled, ...rest } = obj;
		return rest;
	}

	/**
   * Opens discard changes flow.
   *
   */
	openDiscardChangeRuleWarning(): Observable<boolean> {
		return MessageUtility.showConfirmation({
			title: this._translate.instant('translations.operational.areas.Details.DiscardChanges'),
			content: this._translate.instant('translations.operational.areas.Details.Warnings.NotSaved'),
			submitText: this._translate.instant('translations.shared.Continue'),
			cancelText: this._translate.instant('translations.shared.Cancel'),
			width: 450,
			height: 200,
			minWidth: 250
		});
	}


	/**
   * Generate duplicate name based on how many copies we have already.
   *
   * @param item C2oa model.
   * @param c20aList C2oa List.
   * @returns String.
   * @private
   */
	duplicateName(item: C2oa, c20aList: C2oa[]): string {
		const begin = '-Copy(';
		const end = ')';
		const index = item.c2oaName.indexOf(begin);
		const name = index < 0 ? item.c2oaName.trim() : item.c2oaName.trim().substring(0, index);
		const others = c20aList.filter(item => item.c2oaName.toLowerCase().startsWith(name.toLowerCase()));

		if (others.length) {
			const newNo = Math.max(...others.map(item => this.getDuplicateNameNo(item.c2oaName, begin, end)));
			return `${name}${begin}${newNo}${end}`;
		}
		return `${name}${begin}1${end}`;
	}

	/**
   * Generate duplicate name no.
   *
   * @param c2oaName String.
   * @param begin String.
   * @param end String.
   * @returns Number.
   * @private
   */
	getDuplicateNameNo(c2oaName: string, begin: string, end: string): number {
		const regExp = /-Copy((.*))/;
		const matches = regExp.exec(c2oaName);

		if (matches) {
			const no = matches[0].replace(begin, '').replace(end, '');
			return no && !isNaN(Number(no)) ? Number(no) + 1 : 1;
		}
		return 1;
	}
}
