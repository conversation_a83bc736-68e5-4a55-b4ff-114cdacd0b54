/* eslint-disable no-lonely-if */
/* eslint-disable padded-blocks */
/* eslint-disable sort-imports */
/* eslint-disable no-restricted-syntax */
import { Airc2isFunctions, DialogLevelEnum, KeyValueType, ObjectHelper, Permissions } from '@airc2is/aus';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { checkSecurityDemand, detailedDiff, setField } from './operational-areas-details.helper';

import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { C2oa } from 'src/app/types/operational-areas/operational-areas.type';
import { C2oaActionStatusEnum } from '../../services/enum/c2oa-action.enum';
import { C2oaConstantsHelper } from '../../constants/c2oa-constants';
import { C2oaService } from '../../services/operational-areas.service';
import { MessageUtility } from '@airc2is/vscontrols';
import { TranslateService } from '@ngx-translate/core';
import cloneDeep from 'lodash/cloneDeep';
import { debounceTime } from 'rxjs/operators';

/**
 * C2oaDetailsComponent.
 */
@Component({
	selector: 'app-c2oa-details',
	templateUrl: './operational-areas-details.component.html',
	styleUrls: ['./operational-areas-details.component.scss']
})
export class C2oaDetailsComponent implements OnInit, OnChanges {
	@Input() model: C2oa | null = null;
	@Output() nameChange = new EventEmitter<string>();
	@Output() lastSelectedValue = new EventEmitter<{item: C2oa, action: C2oaActionStatusEnum }>();
	@Output() selectedKey = new EventEmitter<number | null>();

	public types = this._c2oaService.Types
		.map<KeyValueType>((type, index) => ({
			value: index,
			key: type.shortName
		}));

	public fields = {
		altitudeMin: setField('AltitudeMin'),
		altitudeMax: setField('AltitudeMax')
	};

	public isEdit = true;
	public isReadonly = false;
	isAltitudeDisabled = false;
	public date = (new Date()).toISOString();

	public validationError = {
		isVisible: false,
		content: ''
	};

	readonly Status: { [value in ApprovalStatusEnum]: { fill: string, label: string, iconUrl: string } } = {
		Draft: {
			fill: '#ddd',
			label: this._translate.instant('translations.operational.areas.Shared.ApprovalStatus.Draft'),
			iconUrl: 'assets/visuals/edit.svg'
		},
		Approved: {
			fill: '#6CC316',
			label: this._translate.instant('translations.operational.areas.Shared.ApprovalStatus.Approved'),
			iconUrl: 'assets/visuals/Images/approve_16.png'

		},
		Submitted: {
			fill: '#ddd',
			label: this._translate.instant('translations.operational.areas.Shared.ApprovalStatus.Submitted'),
			iconUrl: 'assets/visuals/Images/C2OAStatus/submit_16.png'
		}
	};

	private _original: C2oa | null = null;
	private _optionalAltitude = ['JFSCL', 'FSCM', 'FSSL', 'FLOT'];
	private _duplicateName = '';
	private _hasPermission = true;
	private _subscriptions: Subscription[] = [];
	private _inputChangeSubject: Subject<{ text: string; property: 'altitudeMin' | 'altitudeMax'}> = new Subject();

	constructor(
		private _c2oaService: C2oaService,
		private _c2oaConstantsHelper: C2oaConstantsHelper,
		private _translate: TranslateService) {

		this._c2oaService.registerAnswer('isDetailsChanged', (callback) => callback(this.isChanged));

		this._subscriptions.push(
			this._c2oaService.listen.changeSelectionWarning.subscribe(() => {
				this._c2oaConstantsHelper.openDiscardChangeRuleWarning().subscribe((response) => {
					if (response) {
						this._c2oaService.send.canChangeSelection.next(true);
					} else {
						const selectedKey = this.model ? this.model.id as unknown as number : null;
						this.selectedKey.emit(selectedKey);
					}
				});
			}));

		this._subscriptions.push(this._c2oaService.listen.create.subscribe(this._create.bind(this)));
		this._subscriptions.push(this._c2oaService.listen.save.subscribe(this._save.bind(this)));

		this._subscriptions.push(
			this._inputChangeSubject.pipe(debounceTime(500))
				.subscribe(({ text, property }) => {
					this.altCheckAssign(text, property);
				})
		);
	}

	/**
	 * Check if the edit model changes vs the original / first init model.
	 *
	 * @returns Boolean.
	 */
	public get isChanged(): boolean {
		if (this.model && this.model.isNew && !this._original) {
			const isDifferent = JSON.stringify(this._c2oaConstantsHelper.omitId(this.model)) !== JSON.stringify(this._c2oaConstantsHelper.omitId({ ...this._c2oaConstantsHelper.newC2oaModel(true) } as C2oa));
			if (isDifferent) {
				return true;
			}
			return false;
		}

		if (!this.model || !this._original) {
			return false;
		}

		const modelCopy = {
			...this.model,
			...{ coverage: this.model.coverage ?? 0 }
		};
		const originalCopy = {
			...this._original,
			...{ coverage: this._original.coverage ?? 0 }
		};

		const different = JSON.stringify(modelCopy) !== JSON.stringify(originalCopy);

		if (different) {
			// console.log('Got difference in model vs original: ', detailedDiff(this.model, this._original));
		}

		return different;
	}

	/**
	 * Checks if model is valid for save.
	 *
	 * @returns Boolean.
	 * @private
	 */
	private get _isModelValid(): boolean {
		this.validationError.content = '';
		this.validationError.isVisible = false;

		if (!this.model) {
			return false;
		}

		const prefix = `translations.operational.areas.Details.Warnings.`;

		if (this.model.alertEnabled) {
			if (!this.model.affiliation || !this.model.objectType) {
				this.validationError.content = this._translate.instant(`${prefix}AlertEnabledMissing`);
			}
		}

		if (!this.model.location || !Object.keys(this.model.location).length) {
			this.validationError.content = this._translate.instant(`${prefix}LocationMissing`);
		}

		const missingAltMax = this.fields.altitudeMax.required && !this.model.altitudeMax;
		const missingAltMin = this.fields.altitudeMin.required && !this.model.altitudeMin;

		if (missingAltMax || missingAltMin) {
			this.validationError.content = this._translate.instant(`${prefix}AltitudeMissing`);
		}

		if (!this.model.c2oaType) {
			this.validationError.content = this._translate.instant(`${prefix}TypeMissing`);
		}

		if (!this.model.c2oaName) {
			this.validationError.content = this._translate.instant(`${prefix}NameMissing`);
		}

		if (this.validationError.content) {
			this.validationError.isVisible = true;
			MessageUtility.showMessage({
				title: this._translate.instant('translations.shared.Information'),
				content: this.validationError.content,
				level: DialogLevelEnum.Info,
				helpParameter: '#help_param', // TODO: replace help param
				buttonText: this._translate.instant('translations.shared.Close'),
				width: 450,
				height: 200
			});
			return false;
		}

		return true;
	}

	/**
	 * Checks if altitude is mandatory.
	 *
	 * @returns Boolean.
	 * @private
	 */
	private get _isAltMandatory(): boolean {
		return !this._optionalAltitude.includes(this.model?.c2oaType || '');
	}

	/**
	 * Initialize methods.
	 */
	ngOnInit(): void {
		this._hasPermission = checkSecurityDemand(Airc2isFunctions.C2oa, Permissions.Update);

		if (ObjectHelper.isNullOrEmpty(this.model)) {
			this.model = { ...this._c2oaConstantsHelper.newC2oaModel(true) } as C2oa;
		}
	}

	/**
	 * When model changes.
	 *
	 * @param changes
	 */
	ngOnChanges(changes: SimpleChanges): void {
		if (!ObjectHelper.isNullOrEmpty(changes.model.currentValue)) {
			this._getC2oaById(changes.model.currentValue.id);
		}
	}

	/**
	 * Hide validation errors on layout click if click is not in the targeted input.
	 *
	 * @param event Event.
	 */
	public layoutClick(event: Event): void {
		Object.values(this.fields)
			.forEach((obj) =>
				obj.error &&
				(obj.showError = event.target ? Boolean((event.target as HTMLElement).closest(this._altElementSelector(obj.name))) : false));
	}


	/**
	 * Validation on altitude max and min, if all good, value is assigned to model.
	 *
	 * @param event The inserted text value.
	 * @param property String 'altitudeMin' or 'altitudeMax'.
	 */
	onInputChange(event: string, property: 'altitudeMin' | 'altitudeMax'): void {
		let text = event;
		text = event.toUpperCase();
		event = text;
		this._inputChangeSubject.next({
			text,
			property
		});
	}

	/**
	 * Validation on altitude max and min, if all good, value is assigned to model.
	 *
	 * @param text String.
	 * @param property String 'altitudeMin' or 'altitudeMax'.
	 */
	public altCheckAssign(text: string, property: 'altitudeMin' | 'altitudeMax'): void {
		this.fields[property].error = false;
		this.fields[property].showError = false;

		const units = [
			{ regex: /^FL([0-999]+)$/ },
			{ regex: /^([0-99900]+)AMSL$/ },
			{ regex: /^MSL$/ },
			{ regex: /^([0-99900]+)AGL$/ },
			{ regex: /^GL$/ }
		];

		const match = units?.find(unit => text?.match(unit.regex));


		if ((text && !match) || (!text && this._isAltMandatory)) {
			this.fields[property].error = true;
			this.fields[property].showError = this.fields[property].error &&
				(document.querySelector(this._altElementSelector(this.fields[property].name, ' kendo-textbox'))?.classList.contains('k-focus') ?? false);
		}

		if (!this.fields[property].error && this.model) {
			this.model[property] = text;
		}
	}

	/**
	 * Set mandatory/optional alt based on type change.
	 */
	public typeValueChange(): void {
		if (this.model) {
			this.fields.altitudeMin = setField('AltitudeMin', this._isAltMandatory);
			this.fields.altitudeMax = setField('AltitudeMax', this._isAltMandatory);

			if (this._isAltMandatory === false) {
				this.model.altitudeMin = '';
				this.model.altitudeMax = '';
				this.isAltitudeDisabled = true;
			} else {
				this.altCheckAssign(this.model.altitudeMax || '', 'altitudeMax');
				this.altCheckAssign(this.model.altitudeMin || '', 'altitudeMin');
				this.isAltitudeDisabled = false;
			}

			this._c2oaService.send.edit.next({ ...this.model });
			this.model = { ...this.model };
		}
	}

	/**
	 * On Status Changed, refresh the entity.
	 *
	 * @param item The C2oa item response.
	 */
	public onStatusChange(item: C2oa): void {
		if (this.model?.id) {
			this._getC2oaById(this.model.id);
		}
	}


	/**
	 * On change selection event for the c2oa model.
	 *
	 * @param data { id?: string, duplicateName: string }.
	 * @param data.id String.
	 * @param data.duplicateName String.
	 * @private
	 */
	private _changeSelection(data: { id?: string, duplicateName: string}): void {
		if (data && data.id) {
			this._getC2oaById(data.id);
		} else {
			this._clear();
		}

		this._duplicateName = data.duplicateName;
	}


	/**
	 * Refresh the c2oa model.
	 *
	 * @param id String.
	 * @private
	 */
	private _getC2oaById(id: string): void {

		this.isReadonly = !this._hasPermission;
		this._c2oaService.getById(id)
			.subscribe({
				next: (response) => {
					response.coverage = response.coverage ?? 0;
					response.isPlanned = response.isPlanned ?? false;
					response.isSimulated = response.isSimulated ?? false;

					this.model = response;
					this._original = cloneDeep(response);

					this.nameChange.emit(response.c2oaName);

					if (response.approvalStatus !== ApprovalStatusEnum.Draft) {
						this.isEdit = false;
					} else {
						this.isEdit = true;
					}

					this.isReadonly = [
						ApprovalStatusEnum.Submitted,
						ApprovalStatusEnum.Approved].includes(response.approvalStatus) || !this._hasPermission;

					this.typeValueChange();
				}
			});
	}

	/**
	 * On create event for the c2oa model.
	 *
	 * @param isDuplicate Boolean. True means DUPLICATE, FALSE means CREATE.
	 * @private
	 */
	private _create(isDuplicate: boolean): void {
		if (!this.model) {
			return;
		}

		this._original = null;

		if (isDuplicate) {
			if (this.isChanged) {
				this._c2oaConstantsHelper.openDiscardChangeRuleWarning().subscribe((response) => {
					if (response) {
						this._c2oaService.send.canChangeSelection.next(true);
					}
				});
			} else {
				const data = this._c2oaService.getC2oaDataValue();
				this._clear({ ...this._c2oaConstantsHelper.duplicateModel(this.model, data) } as C2oa);
			}
		} else {
			if (this.isChanged) {
				this._c2oaConstantsHelper.openDiscardChangeRuleWarning().subscribe((response) => {
					if (response) {
						this._c2oaService.send.canChangeSelection.next(true);
					}
				});
			} else {
				this._clear({ ...this._c2oaConstantsHelper.newC2oaModel() } as C2oa);
				this.fields.altitudeMax.error = this._isAltMandatory;
				this.fields.altitudeMin.error = this._isAltMandatory;
			}
		}
	}

	/**
	 * On create event for the c2oa model.
	 *
	 * @private
	 */
	private _save(): void {
		if (!this.model) {
			return;
		}

		if (!this.isChanged && !this.model.isDuplicate) {
			MessageUtility.showMessage({
				title: this._translate.instant('translations.shared.Warning'),
				content: this._translate.instant('translations.operational.areas.Details.Warnings.NoChanges'),
				level: DialogLevelEnum.Warning,
				helpParameter: '#help_param', // TODO: replace help param
				buttonText: this._translate.instant('translations.shared.Cancel'),
				width: 450,
				height: 200
			});
		} else if (this._isModelValid) {

			if (this.model.isDuplicate) {
				this.model.id = '';
			}
			this._c2oaService.save(this.model)
				.subscribe({
					next: (response) => {
						if (this.model?.isDuplicate || this.model?.isNew) {
							this.lastSelectedValue.emit({
								item: response,
								action: C2oaActionStatusEnum.Add
							});
						} else {
							this.lastSelectedValue.emit({
								item: response,
								action: C2oaActionStatusEnum.Update
							});
						}
						this.model = response;
						this._original = response;
					}
				});
		}
	}

	/**
	 * Clear the c2oa model.
	 *
	 * @param newModel C2oa.
	 * @private
	 */
	private _clear(newModel?: C2oa): void {

		this.model = newModel ? { ...newModel } as C2oa : { ...this._c2oaConstantsHelper.newC2oaModel() } as C2oa;

		this.isEdit = false;
		this.isReadonly = false;

		// this._original = newModel ? { ...newModel } : null; // OLD line
		this._duplicateName = '';
		this.nameChange.emit(this.model?.c2oaName || '');
		this._c2oaService.send.edit.next(this.model ? { ...this.model } : null);
	}


	/**
	 * Return the string selector for the alt min/max vs-textboxes.
	 *
	 * @param name String.
	 * @param innerElementSelector String.
	 * @returns String.
	 */
	private _altElementSelector(name: string, innerElementSelector = ''): string {
		return `vs-textbox[name='${name}']${innerElementSelector}`;
	}
}
