/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ActivityStatusEnum } from '../../../services/enum/activity-status.enum';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oaActiveTimePeriod } from 'src/app/types/operational-areas';
import { C2oaService } from '../../../services/operational-areas.service';
import { C2oaTimeComponent } from '../time/operational-areas-time.component';
import { CoreModule } from 'src/app/core/core.module';
import { FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SimpleChange } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { dummySingleGetResponse } from '../../../services/mocks';
import { provideMockStore } from '@ngrx/store/testing';

const period = <C2oaActiveTimePeriod>{
	toDate: new Date(),
	fromDate: new Date(Date.now() - 86400000)
};

describe('C2oaTimeComponent', () => {
	let component: C2oaTimeComponent;
	let fixture: ComponentFixture<C2oaTimeComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, TranslateModule.forRoot(), FormsModule, CoreModule, BrowserAnimationsModule],
					declarations: [C2oaTimeComponent],
					providers: [C2oaService, provideMockStore({})]
				});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaTimeComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());

	it('ngOnChanges', () => {
		component['_activityStatus'] = ActivityStatusEnum.Dormant;

		component.ngOnChanges(
			{ model: new SimpleChange(null, dummySingleGetResponse, true) });

		expect(component.effectiveTimeData).toEqual(dummySingleGetResponse.timePeriods);
	});

	it('ngOnChanges with only readonly', () => {
		component.ngOnChanges(
			{ isReadonly: new SimpleChange(true, false, false) });

		expect(component.effectiveTimeData).toEqual([]);
	});

	it('status click, skipWarning false', () => {
		const option = component.statusRadioBtns[0];
		component.statusClick(option);
	});
	it('status click, skipWarning true', () => {
		component.isActiveOnPeriod = true;
		component.effectiveTimeData = [period];
		const option = component.statusRadioBtns[1];
		component.statusClick(option, true);
	});

	it('status click, skipWarning false, with period', () => {
		component.isActiveOnPeriod = true;
		component.effectiveTimeData = [period];
		const option = component.statusRadioBtns[1];
		component.statusClick(option, false);
	});

	it('from to click', () => {
		const option = component.fromToRadioBtns[0];
		component.fromToClick(option);
	});

	it('from to click with default null', () => {
		const option = component.fromToRadioBtns[0];
		// eslint-disable-next-line no-undefined
		option.isDefault = undefined;
		component.fromToClick(option);
	});

	it('add', () => {
		component.isActiveOnPeriod = true;
		component.effectiveTimeData = [];
		component.dates.from = period.fromDate as Date;
		component.dates.to = period.toDate as Date;
		component.add();
	});

	it('add, isActiveOnPeriod false', () => {
		component.isActiveOnPeriod = false;
		component.add();
	});

	it('remove, defaults optional', () => {
		component.effectiveTimeData = [period];
		component.remove(period);
	});

	it('remove, true on skipWarning', () => {
		component.effectiveTimeData = [period];
		component.remove(period, true, false);
	});

	it('remove, true on skipWarning, true on canRemove', () => {
		component.effectiveTimeData = [period];
		component.remove(period, true, true);
	});

	it('remove all, false on skip warning', () => {
		const option = component.statusRadioBtns[0];
		component.removeAll(false, option);

		component.removeWarning.closeCallback(true);
		component.removeWarning.closeCallback(false);
	});

	it('remove all, true on skip warning', () => {
		const option = component.statusRadioBtns[0];
		component.removeAll(true, option);
	});

	it('valid dates with has from selected, from higher than to', () => {
		const hasFrom = component.fromToRadioBtns.find(btn => btn.isDefault);
		// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
		hasFrom!.isSelected = true;
		component.dates.from = period.toDate as Date;
		component.dates.to = period.fromDate as Date;

		component['_validDates']();
	});

	it('valid dates, overlapping period', () => {
		component.dates.from = period.fromDate as Date;
		component.dates.to = period.toDate as Date;
		component.effectiveTimeData = [period];

		component['_validDates']();
	});
});
