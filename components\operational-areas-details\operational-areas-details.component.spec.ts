/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oa } from 'src/app/types/operational-areas';
import { C2oaAlertComponent } from './alert/operational-areas-alert.component';
import { C2oaComponent } from '../operational-areas.component';
import { C2oaDetailsComponent } from './operational-areas-details.component';
import { C2oaGeometryComponent } from './geometry/operational-areas-geometry.component';
import { C2oaService } from '../../services/operational-areas.service';
import { C2oaTimeComponent } from './time/operational-areas-time.component';
import { CoreModule } from 'src/app/core/core.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from 'src/app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

import { dummySingleGetResponse } from '../../services/mocks';
import { of } from 'rxjs';
import { provideMockStore } from '@ngrx/store/testing';


describe('C2oaDetailsComponent', () => {
	let component: C2oaDetailsComponent;
	let fixture: ComponentFixture<C2oaDetailsComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule(
				{
					imports: [HttpClientTestingModule, SharedModule, TranslateModule.forRoot(), CoreModule, BrowserAnimationsModule],
					declarations: [C2oaComponent, C2oaDetailsComponent, C2oaAlertComponent, C2oaGeometryComponent, C2oaTimeComponent],
					providers: [C2oaService, provideMockStore({})]
				});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaDetailsComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());

	it('trigger layoutClick', () => {
		component.fields.altitudeMax.error = true;
		component.layoutClick(new Event('click', {
			bubbles: true,
			cancelable: true
		}));
	});

	it('trigger onWarningClose true', () => {
		component['_pendingCreate'] = false;

		component.onWarningClose(true);

		expect(component['_canContinue']).toEqual(true);
		expect(component.showWarning).toEqual(false);
	});

	it('trigger onWarningClose true, pending create true', () => {
		component['_pendingCreate'] = true;
		component.onWarningClose(true);

		expect(component['_canContinue']).toEqual(true);
		expect(component.showWarning).toEqual(false);

		expect(component.model).toEqual(component['_emptyModel'] as C2oa);
		expect(component.isEdit).toEqual(false);
		expect(component.isReadonly).toEqual(false);

		expect(component['_original']).toEqual(component['_emptyModel'] as C2oa);
		expect(component['_canContinue']).toEqual(true);
		expect(component['_pendingCreate']).toEqual(false);
		expect(component['_duplicateName']).toEqual('');
	});

	it('trigger onWarningClose false, pending create true', () => {
		component['_pendingCreate'] = true;

		component.onWarningClose(false);

		expect(component['_pendingCreate']).toEqual(false);
	});

	it('trigger onWarningClose false', () => {
		component.onWarningClose(false);

		expect(component.showWarning).toEqual(false);
	});

	it('trigger private _changeSelection', () => {
		const name = 'Duplicate Name';

		component['_changeSelection'](
			{
				id: '2',
				duplicateName: name
			});

		expect(component['_duplicateName']).toEqual(name);
		expect(component['_canContinue']).toEqual(!component.isChanged);
	});

	it('trigger private _changeSelection, canContinue true and no id', () => {
		const name = 'Duplicate Name';
		component.model = {
			...dummySingleGetResponse,
			...{ description: 'Alabala' }
		};
		component['_original'] = dummySingleGetResponse;
		component['_canContinue'] = true;
		component['_changeSelection'](
			{
				// eslint-disable-next-line no-undefined
				id: undefined,
				duplicateName: name
			});

		component.nameChange.subscribe((response) => {
			expect(response).toEqual(component.model?.c2oaName as string);
		});
	});

	it('trigger private _create', () => {
		component['_create'](false);
	});

	it('trigger private _create, model isChanged', () => {
		component.model = {
			...dummySingleGetResponse,
			...{ description: 'Alabala' }
		};
		component['_original'] = dummySingleGetResponse;
		component['_create'](false);
	});

	it('trigger private _create with isDuplicate true', () => {
		component['_create'](true);
	});

	it('trigger private _save no change', () => {
		component.model = dummySingleGetResponse;
		component['_original'] = dummySingleGetResponse;
		component['_save']();

		expect(component.showNoChangesWarning).toEqual(true);
	});

	it('trigger private _save change on description', () => {
		component.model = {
			...dummySingleGetResponse,
			...{ description: 'Alabala' }
		};
		component['_original'] = dummySingleGetResponse;
		component['_save']();

		expect(component.showNoChangesWarning).toEqual(false);
	});

	it('trigger _clear', () => {
		component['_clear']();
	});

	it('trigger altCheckAssign wrong value', () => {
		component.altCheckAssign('dummy text', 'altitudeMin');

		// expect(component.fields.altitudeMin.error).toEqual(true);
		// expect(component.fields.altitudeMin.showError).toEqual(true);
	});

	it('trigger altCheckAssign right value', () => {
		component.model = <C2oa>{};

		const value = 'FL100';
		component.altCheckAssign(value, 'altitudeMin');

		expect(component.fields.altitudeMin.error).toEqual(false);
		expect(component.fields.altitudeMin.showError).toEqual(false);
		expect(component.model.altitudeMin).toEqual(value);
	});

	it('trigger private _refresh', () => {
		const service = fixture.debugElement.injector.get(C2oaService);
		spyOn(service, 'getById').and.returnValue(of(dummySingleGetResponse));

		component['_refresh'](dummySingleGetResponse.id, false);
	});

	it('trigger private _refresh with refresh all true', () => {
		const service = fixture.debugElement.injector.get(C2oaService);
		spyOn(service, 'getById').and.returnValue(of(dummySingleGetResponse));

		component['_refresh'](dummySingleGetResponse.id, true);
	});
});
