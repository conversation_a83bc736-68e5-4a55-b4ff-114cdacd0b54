<div class="flex formArea w-full" *ngIf="shapeSources && model">
        <kendo-gridlayout [rows]="[40, 300]"  [cols]="[400, 'auto']" [gap]="15">
                <kendo-gridlayout-item [col]="1" [row]="1" [colSpan]="2">
                                <ags-shapecontrol [shapeSource]="shapeSources" [agsShape]="ioGeometry"
                                [isShapeViewAlwaysVisible]="true" [editableState]="model.isDisabled || !isReadonly "
                                [isEnabled]="!isReadonly" [unitOfMeasurePreference]="unitOfMeasure"
                                [requiredLabelVisibility]="visible"
                                [detailToggleVisibility]="collapsed" [showShapeValidationErrorMessage]="true"
                                (applyRequested)="shapeControlPropertyChanged($event)"></ags-shapecontrol>
                </kendo-gridlayout-item>
        </kendo-gridlayout>
</div>