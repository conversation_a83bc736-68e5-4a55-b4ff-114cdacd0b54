import { ComponentFixture, TestBed } from '@angular/core/testing';

import { C2oaService } from '../services/operational-areas.service';
import { C2oaSidePanelComponent } from './operational-areas-side-panel.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from 'src/app/shared/shared.module';
import { provideMockStore } from '@ngrx/store/testing';

describe('C2oaSidePanelComponent', () => {
	let component: C2oaSidePanelComponent;
	let fixture: ComponentFixture<C2oaSidePanelComponent>;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule({
				imports: [HttpClientTestingModule, SharedModule],
				providers: [C2oaService, provideMockStore({})],
				declarations: [C2oaSidePanelComponent]
			})
			.compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaSidePanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());

	it('trigger select', () => {
		component.select();
		component.select(component.types[0].shortName);
	});
});
