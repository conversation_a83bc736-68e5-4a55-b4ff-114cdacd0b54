/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { C2oa } from 'src/app/types/operational-areas';
import { C2oaService } from '../../../services/operational-areas.service';
import { C2oaStatusComponent } from './operational-areas-status.component';
import { CoreModule } from 'src/app/core/core.module';
import { DialogResult } from '@progress/kendo-angular-dialog';

import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from 'src/app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

import { of } from 'rxjs';
import { provideMockStore } from '@ngrx/store/testing';

describe('C2oaStatusComponent', () => {
	let component: C2oaStatusComponent;
	let fixture: ComponentFixture<C2oaStatusComponent>;
	let service: C2oaService;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [HttpClientTestingModule, TranslateModule.forRoot(), SharedModule, CoreModule, BrowserAnimationsModule],
			declarations: [C2oaStatusComponent],
			providers: [C2oaService, provideMockStore({})]
		})
			.compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaStatusComponent);
		component = fixture.componentInstance;
		service = TestBed.inject(C2oaService);
		component.model = {
			id: '1',
			approvalComment: null
		} as unknown as C2oa;
		fixture.detectChanges();
	});

	it('should open justify dialog when status changes', () => {
		// Arrange
		const mockDialogResult: DialogResult = { text: 'Justify' };
		// spyOn(dialogService, 'open').and.returnValue({ result: of(mockDialogResult) } as VsDialogRef);
		spyOn(component, '_status' as never);

		// Act
		service.send.status.next(ApprovalStatusEnum.Approved);

		// Assert
		// expect(dialogService.open).toHaveBeenCalled();
		expect(component['_status' as never]).toHaveBeenCalled();
	});

	it('should call statusChanged event when status is changed', () => {
		// Arrange
		spyOn(service, 'changeStatus').and.returnValue(of({} as unknown as C2oa));
		spyOn(component.statusChanged, 'emit');

		// Act
		component['_status'](ApprovalStatusEnum.Approved, 'Test comment');

		// Assert
		expect(service.changeStatus).toHaveBeenCalled();
		expect(component.statusChanged.emit).toHaveBeenCalled();
	});
});
