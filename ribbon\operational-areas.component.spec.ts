/* eslint-disable dot-notation */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ApprovalStatusEnum } from 'src/app/enums/approval-status.enum';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { C2oa } from 'src/app/types/operational-areas';
import { C2oaRibbonComponent } from './operational-areas.component';
import { C2oaService } from '../services/operational-areas.service';
import { CoreModule } from 'src/app/core/core.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from 'src/app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

import { c2oaTypes } from '../services/const/operational-areas.const';
import { dummySingleGetResponse } from '../services/mocks';
import { of } from 'rxjs';
import { provideMockStore } from '@ngrx/store/testing';

describe('C2oaRibbonComponent', () => {
	let component: C2oaRibbonComponent;
	let fixture: ComponentFixture<C2oaRibbonComponent>;
	let service: C2oaService;

	beforeEach(async () => {
		await TestBed
			.configureTestingModule({
				imports: [HttpClientTestingModule, TranslateModule.forRoot(), SharedModule, CoreModule, BrowserAnimationsModule],
				declarations: [C2oaRibbonComponent],
				providers: [C2oaService, provideMockStore({})]
			})
			.compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(C2oaRibbonComponent);
		component = fixture.componentInstance;
		service = TestBed.inject(C2oaService);
		fixture.detectChanges();
	});

	it('should create', () => expect(component).toBeTruthy());

	it('should call ng init subscriber', () => {
		component.ngOnInit();

		service.send.edit.next(dummySingleGetResponse);

		expect(component.c2oaInEdit).toEqual(true);

		service.send.edit.next(null);

		expect(component.c2oaInEdit).toEqual(false);
	});

	it('should have c2oaInEdit default to false', () => expect(component.c2oaInEdit).toEqual(false));
	it('should have c2oaToSave default to false', () => expect(component.c2oaToSave).toEqual(false));

	it('triggers history', () => component.history());

	it('should send duplicate event with true', () => {
		spyOn(service.send.create, 'next');

		component.duplicate();

		expect(service.send.create.next).toHaveBeenCalledWith(true);
	});

	it('should send refresh event with last selected type', () => {
		spyOn(service.send.refresh, 'next');

		const lastSelectedType = c2oaTypes[0].shortName;
		spyOnProperty(service, 'lastSelectedType', 'get').and.returnValue(lastSelectedType);

		component.refresh();

		expect(service.send.refresh.next).toHaveBeenCalledWith(lastSelectedType);
	});

	it('should send save event', () => {
		spyOn(service.send.save, 'next');

		component.save();

		expect(service.send.save.next).toHaveBeenCalledWith(true);
	});

	it('should send new event', () => {
		spyOn(service.send.create, 'next');

		component.new();

		expect(service.send.create.next).toHaveBeenCalledWith(false);
	});

	it('should send status event with the provided value', () => {
		spyOn(service.send.status, 'next');
		const statusValue = ApprovalStatusEnum.Approved;

		component.status(statusValue);

		expect(service.send.status.next).toHaveBeenCalledWith(statusValue);
	});

	it('should call delete if user confirms the dialog', () => {
		const continueText = 'Continue';
		// const result = { result: of({ text: continueText }) } as VsDialogRef;
		// spyOn(dialogService, 'open').and.returnValue(result);
		spyOn(component, '_delete' as never);

		component.remove();

		// expect(dialogService.open).toHaveBeenCalled();
		expect(component['_delete']).toHaveBeenCalled();
	});

	it('should not call delete if user cancels the dialog', () => {
		const cancelText = 'Cancel';
		// const result = { result: of({ text: cancelText }) } as VsDialogRef;
		// spyOn(dialogService, 'open').and.returnValue(result);
		spyOn(component, '_delete' as never);

		component.remove();

		// expect(dialogService.open).toHaveBeenCalled();
		expect(component['_delete']).not.toHaveBeenCalled();
	});

	it('should call remove method and refresh data on success', () => {
		spyOn(service, 'remove').and.returnValue(of('1'));
		spyOn(service.send.refresh, 'next');
		component.c2oaEdit = { id: '1' } as unknown as C2oa;

		component['_delete']();

		expect(service.remove).toHaveBeenCalledWith('1');
		expect(service.send.refresh.next).toHaveBeenCalledWith(service.lastSelectedType);
	});

	it('should handle error when remove method fails', () => {
		spyOn(service, 'remove').and.returnValue(of('1'));
		spyOn(console, 'error');
		component.c2oaEdit = { id: '1' } as unknown as C2oa;

		component['_delete']();

		expect(service.remove).toHaveBeenCalledWith('1');
		expect(console.error).toHaveBeenCalled();
	});
});
